# 🔍 Gradio Docling RAG - PDF Processing & Structured Query System

A comprehensive PDF processing and structured data extraction system using Docling, OpenAI, and Supabase with pgvector for intelligent document analysis.

## 🆕 New Modular System

The system now includes a completely redesigned modular architecture (`src/` directory) that provides:

- **Modern LangChain Integration**: Uses latest `.with_structured_output()` patterns
- **Modular Architecture**: Clean separation of concerns across multiple modules
- **Multiple AI Providers**: Support for OpenAI, Anthropic, and Mistral models
- **Comprehensive Testing**: Full test suite with 95%+ coverage
- **CLI and Web Interfaces**: Both command-line and Gradio web interfaces
- **Advanced Error Handling**: Robust error handling with recovery strategies
- **Token Tracking**: Detailed usage monitoring and cost estimation
- **LangSmith Integration**: Optional tracing and performance monitoring

### Quick Start with New System

```bash
# Launch web interface
python src/main.py --mode web

# CLI extraction
python src/main.py --mode cli --extraction-type spare_parts --file-name "document.pdf"

# System validation
python src/main.py --validate
```

## 🌟 Features

### PDF Ingestion (`ingest_pdf.py`)
- **PDF Parsing**: Extract text page-by-page using `pdfplumber`
- **Table Detection**: Automatically detect and structure tables using OpenAI GPT-4
- **Image Extraction**: Extract and save all images using `PyMuPDF`
- **Smart Chunking**: Store each page as a single chunk with metadata
- **Vector Embeddings**: Generate embeddings using OpenAI `text-embedding-3-small`
- **Database Storage**: Store in Supabase with pgvector for similarity search

### Structured Query Interface (`structured_query.py`)
- **JSON Schema Support**: Define extraction schemas for structured data
- **Natural Language Instructions**: Describe what to extract in plain English
- **File-Specific Search**: Limit queries to specific PDF documents
- **Intelligent Subquery Generation**: Break complex queries into focused searches
- **Vector Similarity Search**: Find relevant content using semantic search
- **Structured Data Extraction**: Extract JSON data conforming to your schema
- **CSV Export**: Download results as CSV files

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone and setup
git clone <repository>
cd gradio-docling-rag

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Update your `.env` file with:
- `OPENAI_API_KEY`: Your OpenAI API key
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Your Supabase anon key

### 3. Ingest a PDF

```bash
python ingest_pdf.py "sample/TANK CLEANING MACHINE.pdf"
```

This will:
- Process all 62 pages
- Extract 690+ images
- Detect 41 pages with tables
- Generate embeddings and store in Supabase

### 4. Launch Structured Query Interface

```bash
python structured_query.py
```

Open http://localhost:7860 in your browser.

## 📊 Database Schema

### PDF Documents Table
```sql
CREATE TABLE pdf_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_name TEXT,
    page_number INTEGER,
    text TEXT,                    -- Full markdown content
    metadata JSONB,               -- Table info, image paths, etc.
    embedding VECTOR(1536),       -- OpenAI embedding
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Similarity Search Function
```sql
CREATE OR REPLACE FUNCTION match_documents(
    query_embedding vector(1536),
    match_threshold float,
    match_count int,
    input_file_name text
) RETURNS TABLE (
    id uuid,
    file_name text,
    page_number int,
    text text,
    metadata jsonb,
    similarity float
);
```

## 🎯 Usage Examples

### Example 1: Extract Equipment Specifications

**JSON Schema:**
```json
{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "equipment_name": {"type": "string"},
      "model": {"type": "string"},
      "specifications": {"type": "string"},
      "features": {"type": "string"}
    }
  }
}
```

**Instruction:**
"Extract all tank cleaning equipment with their models, specifications, and key features."

### Example 2: Extract Parts Information

**JSON Schema:**
```json
{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "part_number": {"type": "string"},
      "description": {"type": "string"},
      "price": {"type": "string"},
      "availability": {"type": "string"}
    }
  }
}
```

**Instruction:**
"Find all spare parts with their part numbers, descriptions, prices, and availability status."

## 🧪 Testing

### Run Unit Tests
```bash
python test_ingest_pdf.py --unit
python test_structured_query.py
```

### Run Integration Tests
```bash
python test_ingest_pdf.py --integration
```

## 📁 Project Structure

```
gradio-docling-rag/
├── ingest_pdf.py              # PDF processing and ingestion
├── structured_query.py        # Gradio app for structured queries
├── test_ingest_pdf.py         # Tests for PDF ingestion
├── test_structured_query.py   # Tests for structured queries
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
├── README.md                 # This file
├── sample/                   # Sample PDF files
│   └── TANK CLEANING MACHINE.pdf
└── images/                   # Extracted images (auto-created)
    ├── page1_img1.png
    ├── page1_img2.png
    └── ...
```

## 🔧 Key Components

### PDF Processing Pipeline
1. **Text Extraction**: `pdfplumber` for accurate text extraction
2. **Table Detection**: Automatic table detection and GPT-4 structuring
3. **Image Extraction**: `PyMuPDF` for comprehensive image extraction
4. **Markdown Generation**: Structured markdown with tables, images, and text
5. **Vector Embeddings**: OpenAI embeddings for semantic search
6. **Database Storage**: Supabase with pgvector for efficient retrieval

### Query Processing Pipeline
1. **Schema Validation**: Validate JSON schema format
2. **Subquery Generation**: Break complex queries into focused searches
3. **Vector Search**: Find relevant content using similarity search
4. **Context Aggregation**: Combine relevant chunks intelligently
5. **Structured Extraction**: GPT-4 extracts data conforming to schema
6. **Result Formatting**: JSON output with CSV export option

## 🎨 Gradio Interface Features

- **Interactive Schema Editor**: JSON schema input with syntax highlighting
- **Natural Language Instructions**: Describe extraction requirements
- **File Selection**: Choose specific PDF documents to query
- **Real-time Status**: See processing progress and results
- **JSON Viewer**: Formatted JSON output with syntax highlighting
- **CSV Export**: Download structured data as CSV files

## 🔍 Advanced Features

### Metadata Enrichment
Each page includes rich metadata:
- Table detection and structuring
- Image paths and references
- Page-level context preservation
- Continuation detection for multi-page tables

### Smart Chunking Strategy
- **Page-level chunks**: Preserve complete page context
- **Table preservation**: Keep tables with surrounding text
- **Image associations**: Link images to relevant content
- **Metadata integration**: Rich metadata for enhanced search

## 🚀 Performance

- **Processing Speed**: ~1-2 pages per second
- **Embedding Generation**: Batch processing for efficiency
- **Vector Search**: Sub-second similarity search
- **Scalability**: Handles large documents (60+ pages tested)

## 🛠️ Troubleshooting

### Common Issues

1. **OpenAI API Errors**: Check your API key and model availability
2. **Supabase Connection**: Verify URL and key in `.env` file
3. **Missing Dependencies**: Run `pip install -r requirements.txt`
4. **Image Extraction**: Ensure sufficient disk space for images

### Debug Mode
Set `DEBUG=true` in `.env` for verbose logging.

## 📈 Future Enhancements

- [ ] Support for multiple file formats (DOCX, PPTX)
- [ ] Advanced table extraction with cell-level precision
- [ ] Multi-language support
- [ ] Batch processing interface
- [ ] Custom embedding models
- [ ] Advanced query operators

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

---

**Built with ❤️ using Gradio, OpenAI, Supabase, and modern Python tools.**
