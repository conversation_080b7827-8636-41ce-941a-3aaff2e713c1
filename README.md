---
title: Marine Equipment Data Extraction System
emoji: 🔍
colorFrom: blue
colorTo: green
sdk: gradio
sdk_version: "5.0.0"
app_file: app.py
pinned: false
license: mit
short_description: data extraction for marine equipment
tags:
  - pdf-processing
  - data-extraction
  - marine-equipment
  - rag
  - langchain
  - supabase
  - gradio
---

# 🔍 Marine Equipment Data Extraction System

An AI-powered PDF processing and structured data extraction system specifically designed for marine equipment documentation. This application uses advanced OCR, natural language processing, and vector search to extract structured data from technical manuals, parts catalogs, and maintenance schedules.

## 🌟 Features

### 📄 PDF Processing
- **Advanced OCR**: Mistral OCR with image extraction capabilities
- **Smart Chunking**: Page-level processing with context preservation
- **Table Detection**: Automatic table structure recognition
- **Vector Embeddings**: Semantic search using OpenAI embeddings
- **Database Storage**: Supabase with pgvector for efficient retrieval

### 🎯 Structured Data Extraction
- **Multiple Extraction Types**: 
  - Spare Parts extraction with part numbers, descriptions, and specifications
  - Component extraction with equipment details and hierarchies
  - Job extraction for maintenance schedules and procedures
- **AI Model Support**: OpenAI GPT-4, Anthropic Claude, and Mistral models
- **Configurable Schemas**: JSON schema-based extraction with customizable prompts
- **Export Options**: JSON and CSV download formats

### 🔧 Advanced Configuration
- **Schema Editor**: Built-in editor for extraction schemas
- **Prompt Management**: Customizable system prompts for different extraction types
- **Search Query Management**: Configurable search queries for better chunk retrieval
- **Token Tracking**: Detailed usage monitoring and cost estimation

## 🚀 Quick Start

### Authentication
This application requires authentication. Use the following credentials:
- **Username**: Set via `GRADIO_USERNAME` environment variable (default: admin)
- **Password**: Set via `GRADIO_PASSWORD` environment variable (default: marine2024)

### Usage Steps
1. **Upload PDF**: Use the "Upload PDF" tab to process marine equipment documents
2. **Configure Extraction**: Select extraction type and AI model in the "Extraction" tab
3. **Extract Data**: Run extraction on processed documents
4. **Download Results**: Export extracted data as JSON or CSV

### Supported Document Types
- Marine equipment manuals
- Parts catalogs and spare parts lists
- Maintenance schedules (PMS)
- Technical specifications
- Equipment drawings and schematics

## 🛠️ Configuration

### Environment Variables
- `GRADIO_USERNAME`: Authentication username
- `GRADIO_PASSWORD`: Authentication password
- `OPENAI_API_KEY`: OpenAI API key for GPT models
- `ANTHROPIC_API_KEY`: Anthropic API key for Claude models
- `MISTRAL_API_KEY`: Mistral API key for Mistral models
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_KEY`: Supabase service key

### Extraction Types
1. **Spare Parts**: Extract part numbers, descriptions, quantities, and specifications
2. **Components**: Extract equipment hierarchies, models, and technical details
3. **Jobs**: Extract maintenance procedures, frequencies, and schedules

## 📊 Technical Architecture

- **Frontend**: Gradio web interface with tabbed navigation
- **Backend**: Python with LangChain for AI orchestration
- **Database**: Supabase with pgvector for vector similarity search
- **AI Models**: Multi-provider support (OpenAI, Anthropic, Mistral)
- **Processing**: Docling and custom OCR pipeline for PDF processing

## 🔒 Security

- Authentication required for all access
- Environment-based configuration
- Secure API key management
- No data persistence beyond session

## 📈 Performance

- Processes 1-2 pages per second
- Sub-second vector similarity search
- Batch processing for efficiency
- Handles large documents (60+ pages tested)

## 🤝 Support

For technical support or questions about marine equipment data extraction, please refer to the documentation or contact the development team.

---

**Built with ❤️ for the marine industry using Gradio, LangChain, Supabase, and modern AI tools.**
