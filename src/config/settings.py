#!/usr/bin/env python3
"""
Application settings and configuration.

This module manages application settings, environment variables,
and configuration constants.
"""

import os
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    supabase_url: str
    supabase_key: str
    table_name: str = "pdf_documents"
    embedding_model: str = "text-embedding-3-small"


@dataclass
class ModelConfig:
    """LLM model configuration settings."""
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    mistral_api_key: Optional[str] = None
    default_model: str = "gpt-4o-mini"
    temperature: float = 0.1
    max_tokens: Optional[int] = None


@dataclass
class UIConfig:
    """UI configuration settings."""
    title: str = "Marine Equipment Data Extraction System"
    description: str = "Extract structured data from marine equipment PDFs"
    theme: str = "default"
    share: bool = False
    debug: bool = False


@dataclass
class LoggingConfig:
    """Logging configuration settings."""
    level: str = "INFO"
    log_file: Optional[str] = None
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


@dataclass
class ExtractionConfig:
    """Extraction configuration settings."""
    max_chunks: int = 10
    preserve_page_order: bool = True
    use_semantic_search: bool = True
    chunk_overlap: int = 200
    chunk_size: int = 1000


class Settings:
    """Application settings manager."""
    
    def __init__(self):
        """Initialize settings from environment variables."""
        self.project_root = Path(__file__).parent.parent.parent
        self.data_dir = self.project_root / "data"
        self.output_dir = self.project_root / "extracted_data"
        self.logs_dir = self.project_root / "logs"
        self.temp_dir = self.project_root / "temp"
        
        # Ensure directories exist
        self.output_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
        
        # Load configurations
        self.database = self._load_database_config()
        self.models = self._load_model_config()
        self.ui = self._load_ui_config()
        self.logging = self._load_logging_config()
        self.extraction = self._load_extraction_config()
    
    def _load_database_config(self) -> DatabaseConfig:
        """Load database configuration from environment."""
        return DatabaseConfig(
            supabase_url=os.getenv("SUPABASE_URL", ""),
            supabase_key=os.getenv("SUPABASE_KEY", ""),
            table_name=os.getenv("SUPABASE_TABLE", "pdf_documents"),
            embedding_model=os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        )
    
    def _load_model_config(self) -> ModelConfig:
        """Load model configuration from environment."""
        return ModelConfig(
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            anthropic_api_key=os.getenv("ANTHROPIC_API_KEY"),
            mistral_api_key=os.getenv("MISTRAL_API_KEY"),
            default_model=os.getenv("LLM_MODEL", "gpt-4o-mini"),
            temperature=float(os.getenv("LLM_TEMPERATURE", "0.1")),
            max_tokens=int(os.getenv("LLM_MAX_TOKENS", "0")) or None
        )
    
    def _load_ui_config(self) -> UIConfig:
        """Load UI configuration from environment."""
        return UIConfig(
            title=os.getenv("UI_TITLE", "Marine Equipment Data Extraction System"),
            description=os.getenv("UI_DESCRIPTION", "Extract structured data from marine equipment PDFs"),
            theme=os.getenv("UI_THEME", "default"),
            share=os.getenv("UI_SHARE", "false").lower() == "true",
            debug=os.getenv("UI_DEBUG", "false").lower() == "true"
        )
    
    def _load_logging_config(self) -> LoggingConfig:
        """Load logging configuration from environment."""
        log_file = os.getenv("LOG_FILE")
        if log_file:
            log_file = str(self.logs_dir / log_file)
        
        return LoggingConfig(
            level=os.getenv("LOG_LEVEL", "INFO"),
            log_file=log_file,
            format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )
    
    def _load_extraction_config(self) -> ExtractionConfig:
        """Load extraction configuration from environment."""
        return ExtractionConfig(
            max_chunks=int(os.getenv("MAX_CHUNKS", "10")),
            preserve_page_order=os.getenv("PRESERVE_PAGE_ORDER", "true").lower() == "true",
            use_semantic_search=os.getenv("USE_SEMANTIC_SEARCH", "true").lower() == "true",
            chunk_overlap=int(os.getenv("CHUNK_OVERLAP", "200")),
            chunk_size=int(os.getenv("CHUNK_SIZE", "1000"))
        )
    
    def get_available_models(self) -> Dict[str, List[str]]:
        """Get available models by provider."""
        models = {
            "openai": [
                "gpt-4.1",
                "gpt-4.1-mini", 
                "gpt-4o",
                "gpt-4o-mini", 
                
            ],
            "anthropic": [
                "claude-opus-4-20250514",
                "claude-sonnet-4-20250514",
            ],
            "mistral": [
                "mistral-large-latest",
            ]
        }
        
        # Filter based on available API keys
        available_models = {}
        
        if self.models.openai_api_key:
            available_models["OpenAI"] = models["openai"]
        
        if self.models.anthropic_api_key:
            available_models["Anthropic"] = models["anthropic"]
        
        if self.models.mistral_api_key:
            available_models["Mistral"] = models["mistral"]
        
        return available_models
    
    def validate_configuration(self) -> List[str]:
        """
        Validate configuration and return list of issues.
        
        Returns:
            List of configuration issues (empty if valid)
        """
        issues = []
        
        # Check database configuration
        if not self.database.supabase_url:
            issues.append("SUPABASE_URL is not set")
        
        if not self.database.supabase_key:
            issues.append("SUPABASE_KEY is not set")
        
        # Check at least one model API key is available
        if not any([
            self.models.openai_api_key,
            self.models.anthropic_api_key,
            self.models.mistral_api_key
        ]):
            issues.append("No LLM API keys are configured")
        
        # Check data directories exist
        if not self.data_dir.exists():
            issues.append(f"Data directory does not exist: {self.data_dir}")
        
        return issues
    
    def get_model_provider(self, model_name: str) -> str:
        """
        Get the provider for a given model name.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Provider name (openai, anthropic, mistral)
        """
        model_name_lower = model_name.lower()
        
        if any(name in model_name_lower for name in ["gpt", "openai"]):
            return "openai"
        elif any(name in model_name_lower for name in ["claude", "anthropic"]):
            return "anthropic"
        elif any(name in model_name_lower for name in ["mistral"]):
            return "mistral"
        else:
            return "openai"  # Default fallback


# Global settings instance
settings = Settings()
