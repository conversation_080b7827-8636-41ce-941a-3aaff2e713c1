#!/usr/bin/env python3
"""
Chunk processor for filtering, deduplication, and aggregation.

This module provides functionality to process retrieved chunks,
including filtering, deduplication, and aggregation operations.
"""

import logging
from typing import List, Dict, Any, Set, Optional, Callable
from collections import defaultdict
import hashlib

from ..core.base_classes import BaseChunkProcessor, ChunkData
from ..core.utils import clean_text_for_processing

logger = logging.getLogger(__name__)


class ChunkProcessor(BaseChunkProcessor):
    """Chunk processor implementation."""

    def __init__(self, max_occurrence_count: int = 3):
        """Initialize chunk processor.

        Args:
            max_occurrence_count: Maximum number of identical chunks to keep (default: 3)
        """
        self.similarity_threshold = 0.8  # Threshold for text similarity
        self.min_chunk_length = 50  # Minimum chunk length to keep
        self.max_chunk_length = 10000  # Maximum chunk length to keep
        self.max_occurrence_count = max_occurrence_count  # Maximum number of identical chunks to keep
    
    def filter_chunks(self, chunks: List[ChunkData], criteria: Dict[str, Any]) -> List[ChunkData]:
        """
        Filter chunks based on criteria.
        
        Args:
            chunks: List of chunks to filter
            criteria: Filtering criteria
            
        Returns:
            Filtered list of chunks
        """
        if not chunks:
            return []
        
        filtered_chunks = chunks.copy()
        
        # Filter by file name
        if "file_names" in criteria and criteria["file_names"]:
            file_names = set(criteria["file_names"])
            filtered_chunks = [chunk for chunk in filtered_chunks if chunk.file_name in file_names]
            logger.debug(f"Filtered by file names: {len(filtered_chunks)} chunks remaining")
        
        # Filter by page range
        if "page_range" in criteria and criteria["page_range"]:
            min_page, max_page = criteria["page_range"]
            filtered_chunks = [
                chunk for chunk in filtered_chunks 
                if min_page <= chunk.page_number <= max_page
            ]
            logger.debug(f"Filtered by page range: {len(filtered_chunks)} chunks remaining")
        
        # Filter by minimum text length
        min_length = criteria.get("min_length", self.min_chunk_length)
        filtered_chunks = [
            chunk for chunk in filtered_chunks 
            if len(chunk.text_raw.strip()) >= min_length
        ]
        logger.debug(f"Filtered by min length: {len(filtered_chunks)} chunks remaining")
        
        # Filter by maximum text length
        max_length = criteria.get("max_length", self.max_chunk_length)
        filtered_chunks = [
            chunk for chunk in filtered_chunks 
            if len(chunk.text_raw.strip()) <= max_length
        ]
        logger.debug(f"Filtered by max length: {len(filtered_chunks)} chunks remaining")
        
        # Filter by similarity score threshold
        if "min_similarity" in criteria and criteria["min_similarity"] is not None:
            min_similarity = criteria["min_similarity"]
            filtered_chunks = [
                chunk for chunk in filtered_chunks 
                if chunk.similarity_score is None or chunk.similarity_score >= min_similarity
            ]
            logger.debug(f"Filtered by similarity: {len(filtered_chunks)} chunks remaining")
        
        # Filter by keywords (must contain at least one)
        if "keywords" in criteria and criteria["keywords"]:
            keywords = [kw.lower() for kw in criteria["keywords"]]
            filtered_chunks = [
                chunk for chunk in filtered_chunks
                if any(keyword in chunk.text_raw.lower() for keyword in keywords)
            ]
            logger.debug(f"Filtered by keywords: {len(filtered_chunks)} chunks remaining")
        
        # Filter by exclude keywords (must not contain any)
        if "exclude_keywords" in criteria and criteria["exclude_keywords"]:
            exclude_keywords = [kw.lower() for kw in criteria["exclude_keywords"]]
            filtered_chunks = [
                chunk for chunk in filtered_chunks
                if not any(keyword in chunk.text_raw.lower() for keyword in exclude_keywords)
            ]
            logger.debug(f"Filtered by exclude keywords: {len(filtered_chunks)} chunks remaining")
        
        logger.info(f"Filtered chunks: {len(chunks)} -> {len(filtered_chunks)}")
        return filtered_chunks
    
    def deduplicate_chunks(self, chunks: List[ChunkData]) -> List[ChunkData]:
        """
        Remove duplicate chunks based on content similarity.
        
        Args:
            chunks: List of chunks to deduplicate
            
        Returns:
            Deduplicated list of chunks
        """
        if not chunks:
            return []
        
        # Use multiple deduplication strategies
        chunks = self._deduplicate_by_hash(chunks)
        chunks = self._deduplicate_by_similarity(chunks)
        chunks = self._deduplicate_by_page_overlap(chunks)
        
        logger.info(f"Deduplicated chunks: {len(chunks)} unique chunks remaining")
        return chunks
    
    def _deduplicate_by_hash(self, chunks: List[ChunkData]) -> List[ChunkData]:
        """Remove exact duplicates using content hash, keeping up to max_occurrence_count chunks per hash."""
        from collections import defaultdict

        hash_to_chunks = defaultdict(list)

        # Group chunks by content hash
        for chunk in chunks:
            cleaned_text = clean_text_for_processing(chunk.text_raw)
            content_hash = hashlib.md5(cleaned_text.encode()).hexdigest()
            hash_to_chunks[content_hash].append(chunk)

        unique_chunks = []
        for content_hash, chunk_group in hash_to_chunks.items():
            if len(chunk_group) <= self.max_occurrence_count:
                # Keep all chunks if within limit
                unique_chunks.extend(chunk_group)
                if len(chunk_group) > 1:
                    logger.debug(f"Hash deduplication: kept {len(chunk_group)} identical chunks (within max limit)")
            else:
                # Too many occurrences, keep only the best ones up to max_occurrence_count
                # Sort by similarity score (descending) and original order (ascending)
                sorted_chunks = sorted(
                    chunk_group,
                    key=lambda c: (c.similarity_score or 0, -chunks.index(c)),
                    reverse=True
                )
                kept_chunks = sorted_chunks[:self.max_occurrence_count]
                unique_chunks.extend(kept_chunks)
                logger.debug(f"Hash deduplication: kept {len(kept_chunks)} out of {len(chunk_group)} identical chunks (max limit applied)")

        logger.debug(f"Hash deduplication: {len(chunks)} -> {len(unique_chunks)}")
        return unique_chunks

    def set_max_occurrence_count(self, count: int) -> None:
        """Set the maximum number of identical chunks to keep.

        Args:
            count: Maximum occurrence count (must be >= 1)
        """
        if count < 1:
            raise ValueError("Max occurrence count must be at least 1")
        self.max_occurrence_count = count
        logger.debug(f"Set max occurrence count to {count}")

    def _deduplicate_by_similarity(self, chunks: List[ChunkData]) -> List[ChunkData]:
        """Remove similar chunks using text similarity."""
        if len(chunks) <= 1:
            return chunks
        
        unique_chunks = []
        
        for chunk in chunks:
            is_duplicate = False
            cleaned_text = clean_text_for_processing(chunk.text_raw)
            
            for existing_chunk in unique_chunks:
                existing_cleaned = clean_text_for_processing(existing_chunk.text_raw)
                
                # Calculate simple similarity (Jaccard similarity)
                similarity = self._calculate_text_similarity(cleaned_text, existing_cleaned)
                
                if similarity > self.similarity_threshold:
                    is_duplicate = True
                    # Keep the chunk with higher similarity score if available
                    if (chunk.similarity_score is not None and 
                        existing_chunk.similarity_score is not None and
                        chunk.similarity_score > existing_chunk.similarity_score):
                        # Replace existing chunk with current one
                        unique_chunks.remove(existing_chunk)
                        unique_chunks.append(chunk)
                    break
            
            if not is_duplicate:
                unique_chunks.append(chunk)
        
        logger.debug(f"Similarity deduplication: {len(chunks)} -> {len(unique_chunks)}")
        return unique_chunks
    
    def _deduplicate_by_page_overlap(self, chunks: List[ChunkData]) -> List[ChunkData]:
        """Remove chunks that are likely from overlapping page content."""
        if len(chunks) <= 1:
            return chunks
        
        # Group chunks by file
        file_chunks: Dict[str, List[ChunkData]] = defaultdict(list)
        for chunk in chunks:
            file_chunks[chunk.file_name].append(chunk)
        
        unique_chunks = []
        
        for file_name, file_chunk_list in file_chunks.items():
            # Sort by page number
            file_chunk_list.sort(key=lambda x: x.page_number)
            
            # Remove overlapping consecutive pages with similar content
            filtered_file_chunks = []
            
            for i, chunk in enumerate(file_chunk_list):
                is_overlap = False
                
                # Check overlap with previous chunk
                if i > 0:
                    prev_chunk = file_chunk_list[i - 1]
                    if (abs(chunk.page_number - prev_chunk.page_number) <= 1 and
                        self._calculate_text_similarity(chunk.text_raw, prev_chunk.text_raw) > 0.7):
                        is_overlap = True
                
                # Check overlap with next chunk
                if i < len(file_chunk_list) - 1:
                    next_chunk = file_chunk_list[i + 1]
                    if (abs(chunk.page_number - next_chunk.page_number) <= 1 and
                        self._calculate_text_similarity(chunk.text_raw, next_chunk.text_raw) > 0.7):
                        is_overlap = True
                
                if not is_overlap:
                    filtered_file_chunks.append(chunk)
            
            unique_chunks.extend(filtered_file_chunks)
        
        logger.debug(f"Page overlap deduplication: {len(chunks)} -> {len(unique_chunks)}")
        return unique_chunks
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate Jaccard similarity between two texts.
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            Similarity score between 0 and 1
        """
        if not text1 or not text2:
            return 0.0
        
        # Convert to sets of words
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        # Calculate Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def aggregate_chunks(self, chunk_lists: List[List[ChunkData]]) -> List[ChunkData]:
        """
        Aggregate multiple chunk lists into one deduplicated list.
        
        Args:
            chunk_lists: List of chunk lists to aggregate
            
        Returns:
            Aggregated and deduplicated list of chunks
        """
        if not chunk_lists:
            return []
        
        # Flatten all chunk lists
        all_chunks = []
        for chunk_list in chunk_lists:
            all_chunks.extend(chunk_list)
        
        if not all_chunks:
            return []
        
        # Deduplicate the aggregated chunks
        unique_chunks = self.deduplicate_chunks(all_chunks)
        
        # Sort by file name and page number for consistent ordering
        unique_chunks.sort(key=lambda x: (x.file_name, x.page_number))
        
        logger.info(f"Aggregated {len(chunk_lists)} chunk lists into {len(unique_chunks)} unique chunks")
        return unique_chunks
    
    def sort_chunks(self, chunks: List[ChunkData], 
                   sort_by: str = "page_number", reverse: bool = False) -> List[ChunkData]:
        """
        Sort chunks by specified criteria.
        
        Args:
            chunks: List of chunks to sort
            sort_by: Sort criteria (page_number, similarity_score, file_name)
            reverse: Whether to sort in reverse order
            
        Returns:
            Sorted list of chunks
        """
        if not chunks:
            return []
        
        sort_functions = {
            "page_number": lambda x: (x.file_name, x.page_number),
            "similarity_score": lambda x: x.similarity_score or 0.0,
            "file_name": lambda x: x.file_name,
            "text_length": lambda x: len(x.text_raw)
        }
        
        if sort_by not in sort_functions:
            logger.warning(f"Unknown sort criteria: {sort_by}, using page_number")
            sort_by = "page_number"
        
        sorted_chunks = sorted(chunks, key=sort_functions[sort_by], reverse=reverse)
        logger.debug(f"Sorted {len(chunks)} chunks by {sort_by}")
        
        return sorted_chunks
    
    def get_chunk_statistics(self, chunks: List[ChunkData]) -> Dict[str, Any]:
        """
        Get statistics about a list of chunks.
        
        Args:
            chunks: List of chunks to analyze
            
        Returns:
            Dictionary with chunk statistics
        """
        if not chunks:
            return {
                "total_chunks": 0,
                "unique_files": 0,
                "page_range": None,
                "avg_text_length": 0,
                "total_text_length": 0
            }
        
        # Calculate statistics
        file_names = set(chunk.file_name for chunk in chunks)
        page_numbers = [chunk.page_number for chunk in chunks if chunk.page_number > 0]
        text_lengths = [len(chunk.text_raw) for chunk in chunks]
        similarity_scores = [chunk.similarity_score for chunk in chunks if chunk.similarity_score is not None]
        
        stats = {
            "total_chunks": len(chunks),
            "unique_files": len(file_names),
            "file_names": sorted(list(file_names)),
            "page_range": (min(page_numbers), max(page_numbers)) if page_numbers else None,
            "avg_text_length": sum(text_lengths) / len(text_lengths) if text_lengths else 0,
            "total_text_length": sum(text_lengths),
            "min_text_length": min(text_lengths) if text_lengths else 0,
            "max_text_length": max(text_lengths) if text_lengths else 0,
            "avg_similarity_score": sum(similarity_scores) / len(similarity_scores) if similarity_scores else None,
            "chunks_with_similarity": len(similarity_scores)
        }
        
        return stats
