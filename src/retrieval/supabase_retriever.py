#!/usr/bin/env python3
"""
Supabase retriever for PDF document chunks.

This module provides functionality to retrieve PDF chunks from Supabase
with support for semantic search and ordered retrieval.
"""

import logging
from typing import List, Optional, Dict, Any
from supabase import create_client, Client
from openai import OpenAI

from ..core.base_classes import BaseRetriever, ChunkData, RetrievalError
from ..config.settings import settings

logger = logging.getLogger(__name__)


class SupabaseRetriever(BaseRetriever):
    """Supabase-based document retriever."""
    
    def __init__(self, supabase_url: Optional[str] = None, supabase_key: Optional[str] = None,
                 openai_api_key: Optional[str] = None):
        """
        Initialize Supabase retriever.
        
        Args:
            supabase_url: Supabase URL (defaults to settings)
            supabase_key: Supabase key (defaults to settings)
            openai_api_key: OpenAI API key for embeddings (defaults to settings)
        """
        self.supabase_url = supabase_url or settings.database.supabase_url
        self.supabase_key = supabase_key or settings.database.supabase_key
        self.table_name = settings.database.table_name
        self.embedding_model = settings.database.embedding_model
        
        if not self.supabase_url or not self.supabase_key:
            raise RetrievalError("Supabase URL and key must be provided")
        
        # Initialize clients
        try:
            self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
            logger.info("Supabase client initialized successfully")
        except Exception as e:
            raise RetrievalError(f"Failed to initialize Supabase client: {e}")
        
        # Initialize OpenAI client for embeddings (if available)
        self.openai_client = None
        openai_key = openai_api_key or settings.models.openai_api_key
        if openai_key:
            try:
                self.openai_client = OpenAI(api_key=openai_key)
                logger.info("OpenAI client initialized for embeddings")
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}")
    
    def get_available_files(self) -> List[str]:
        """
        Get list of available files in the database.
        
        Returns:
            List of unique file names
            
        Raises:
            RetrievalError: If database query fails
        """
        try:
            response = self.supabase.table(self.table_name).select("file_name").execute()
            
            if not response.data:
                logger.warning("No files found in database")
                return []
            
            # Extract unique file names
            file_names = list(set(row["file_name"] for row in response.data if row.get("file_name")))
            file_names.sort()
            
            logger.info(f"Found {len(file_names)} files in database")
            return file_names
            
        except Exception as e:
            raise RetrievalError(f"Failed to get available files: {e}")
    
    def semantic_search(self, query: str, file_name: str, max_chunks: int = 10) -> List[ChunkData]:
        """
        Perform semantic search for relevant chunks.
        
        Args:
            query: Search query
            file_name: Name of the file to search in
            max_chunks: Maximum number of chunks to return
            
        Returns:
            List of relevant chunks with similarity scores
            
        Raises:
            RetrievalError: If search fails
        """
        if not query.strip():
            logger.warning("Empty search query provided")
            return []
        
        if not self.openai_client:
            logger.warning("OpenAI client not available, falling back to text search")
            return self._text_search(query, file_name, max_chunks)
        
        try:
            # Generate embedding for the query
            logger.debug(f"Generating embedding for query: '{query}' using model: {self.embedding_model}")
            embedding_response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=query
            )
            query_embedding = embedding_response.data[0].embedding
            logger.debug(f"Generated embedding with {len(query_embedding)} dimensions")
            
            # Perform vector similarity search
            logger.debug(f"Calling match_documents RPC with threshold=0.01, count={max_chunks}, file={file_name}")
            response = self.supabase.rpc(
                'match_documents',
                {
                    'query_embedding': query_embedding,
                    'match_threshold': 0.01,  # Lower similarity threshold for better matches
                    'match_count': max_chunks,
                    'input_file_name': file_name
                }
            ).execute()

            logger.debug(f"RPC response: {len(response.data) if response.data else 0} results")
            if not response.data:
                logger.info(f"No semantic matches found for query: '{query}' in file: {file_name}")
                return []
            
            # Convert to ChunkData objects
            chunks = []
            for row in response.data:
                chunk = ChunkData(
                    text=row.get("text", ""),
                    text_raw=row.get("text_raw", row.get("text", "")),
                    page_number=row.get("page_number", 0),
                    file_name=row.get("file_name", ""),
                    metadata=row.get("metadata", {}),
                    similarity_score=row.get("similarity", 0.0)
                )
                chunks.append(chunk)
            
            # Log page numbers of retrieved chunks
            page_numbers = [chunk.page_number for chunk in chunks]
            logger.info(f"Found {len(chunks)} semantic matches for query: '{query}' on pages: {sorted(set(page_numbers))}")
            return chunks
            
        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            # Fallback to text search
            return self._text_search(query, file_name, max_chunks)
    
    def _text_search(self, query: str, file_name: str, max_chunks: int = 10) -> List[ChunkData]:
        """
        Perform text-based search as fallback.
        
        Args:
            query: Search query
            file_name: Name of the file to search in
            max_chunks: Maximum number of chunks to return
            
        Returns:
            List of matching chunks
        """
        try:
            # Use PostgreSQL full-text search
            response = self.supabase.table(self.table_name).select("*").eq(
                "file_name", file_name
            ).text_search(
                "text_raw", query
            ).execute()
            
            if not response.data:
                logger.info(f"No text matches found for query: '{query}' in file: {file_name}")
                return []
            
            # Convert to ChunkData objects and limit results
            chunks = []
            for row in response.data[:max_chunks]:  # Limit results here
                chunk = ChunkData(
                    text=row.get("text", ""),
                    text_raw=row.get("text_raw", row.get("text", "")),
                    page_number=row.get("page_number", 0),
                    file_name=row.get("file_name", ""),
                    metadata=row.get("metadata", {}),
                    similarity_score=None  # No similarity score for text search
                )
                chunks.append(chunk)

            logger.info(f"Found {len(chunks)} text matches for query: '{query}' in file: {file_name}")
            return chunks
            
        except Exception as e:
            raise RetrievalError(f"Text search failed: {e}")
    
    def get_file_pages_ordered(self, file_name: str) -> List[ChunkData]:
        """
        Get all pages from a file in ascending order.
        
        Args:
            file_name: Name of the file
            
        Returns:
            List of chunks ordered by page number
            
        Raises:
            RetrievalError: If retrieval fails
        """
        try:
            response = self.supabase.table(self.table_name).select("*").eq(
                "file_name", file_name
            ).order("page_number", desc=False).execute()
            
            if not response.data:
                logger.warning(f"No pages found for file: {file_name}")
                return []
            
            # Convert to ChunkData objects
            chunks = []
            for row in response.data:
                chunk = ChunkData(
                    text=row.get("text", ""),
                    text_raw=row.get("text_raw", row.get("text", "")),
                    page_number=row.get("page_number", 0),
                    file_name=row.get("file_name", ""),
                    metadata=row.get("metadata", {})
                )
                chunks.append(chunk)
            
            logger.info(f"Retrieved {len(chunks)} pages for file: {file_name}")
            return chunks
            
        except Exception as e:
            raise RetrievalError(f"Failed to get file pages: {e}")
    
    def get_multiple_files_pages(self, file_names: List[str]) -> Dict[str, List[ChunkData]]:
        """
        Get pages from multiple files.
        
        Args:
            file_names: List of file names
            
        Returns:
            Dictionary mapping file names to their chunks
        """
        results = {}
        
        for file_name in file_names:
            try:
                chunks = self.get_file_pages_ordered(file_name)
                results[file_name] = chunks
            except RetrievalError as e:
                logger.error(f"Failed to get pages for {file_name}: {e}")
                results[file_name] = []
        
        return results
    
    def search_multiple_queries(self, queries: List[str], file_name: str,
                              max_chunks_per_query: int = 5) -> List[ChunkData]:
        """
        Search using multiple queries and aggregate results.

        Args:
            queries: List of search queries
            file_name: Name of the file to search in
            max_chunks_per_query: Maximum chunks per query

        Returns:
            Aggregated list of unique chunks
        """
        all_chunks, _ = self.search_multiple_queries_with_frequencies(queries, file_name, max_chunks_per_query)
        return all_chunks

    def search_multiple_queries_with_frequencies(self, queries: List[str], file_name: str,
                                               max_chunks_per_query: int = 5) -> tuple[List[ChunkData], dict]:
        """
        Search using multiple queries and aggregate results with frequency tracking.

        Args:
            queries: List of search queries
            file_name: Name of the file to search in
            max_chunks_per_query: Maximum chunks per query

        Returns:
            Tuple of (aggregated list of unique chunks, page frequency dict)
        """
        from collections import Counter

        all_chunks = []
        seen_chunks = set()
        page_frequencies = Counter()

        for query in queries:
            if not query.strip():
                continue

            try:
                chunks = self.semantic_search(query.strip(), file_name, max_chunks_per_query)

                # Count page frequencies from all search results
                for chunk in chunks:
                    page_key = f"{chunk.file_name}_{chunk.page_number}"
                    page_frequencies[page_key] += 1

                # Add unique chunks
                for chunk in chunks:
                    # Create a unique identifier for the chunk
                    chunk_id = f"{chunk.file_name}_{chunk.page_number}_{hash(chunk.text_raw[:100])}"

                    if chunk_id not in seen_chunks:
                        seen_chunks.add(chunk_id)
                        all_chunks.append(chunk)

            except Exception as e:
                logger.error(f"Search failed for query '{query}': {e}")
                continue

        # Sort by page number to maintain document order
        all_chunks.sort(key=lambda x: x.page_number)

        # Log page numbers of all retrieved chunks
        page_numbers = [chunk.page_number for chunk in all_chunks]
        logger.info(f"Found {len(all_chunks)} unique chunks from {len(queries)} queries on pages: {sorted(set(page_numbers))}")

        return all_chunks, dict(page_frequencies)
    
    def health_check(self) -> bool:
        """
        Check if the Supabase connection is healthy.
        
        Returns:
            True if connection is healthy, False otherwise
        """
        try:
            # Try to query the table
            response = self.supabase.table(self.table_name).select("file_name").range(0, 0).execute()
            return True
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
