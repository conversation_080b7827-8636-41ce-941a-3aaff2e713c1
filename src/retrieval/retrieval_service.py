"""
Shared retrieval service for both CLI and UI.
Centralizes chunk retrieval logic to avoid duplication.
"""

import logging
from typing import List, Optional
from ..config.config_manager import ExtractionConfiguration
from .supabase_retriever import SupabaseRetriever
from .chunk_processor import ChunkProcessor
from ..core.base_classes import ChunkData

logger = logging.getLogger(__name__)


class RetrievalService:
    """Shared service for retrieving and processing chunks."""
    
    def __init__(self):
        """Initialize the retrieval service."""
        self.retriever = SupabaseRetriever()
        self.processor = ChunkProcessor()
    
    def retrieve_chunks(self,
                       file_name: str,
                       config: ExtractionConfiguration,
                       max_chunks: int = 20,
                       use_search_queries: bool = True) -> List[ChunkData]:
        """
        Retrieve and process chunks for extraction.
        
        Args:
            file_name: Name of the file to process
            config: Extraction configuration
            max_chunks: Maximum total chunks to return after deduplication
            use_search_queries: Whether to use search queries or get all pages
            
        Returns:
            List of processed chunks ready for extraction
        """
        logger.info(f"Retrieving chunks from {file_name}...")
        
        # Retrieve raw chunks
        page_frequencies = {}
        if use_search_queries and config.search_queries:
            chunks, page_frequencies = self._retrieve_with_search_queries(
                file_name, config, max_chunks
            )
        else:
            chunks = self._retrieve_all_pages(file_name, max_chunks)
            # For all pages retrieval, each page appears once
            page_frequencies = {f"{file_name}_{chunk.page_number}": 1 for chunk in chunks}

        if not chunks:
            logger.warning(f"No chunks found for file: {file_name}")
            return []

        # Process and deduplicate chunks
        processed_chunks = self.processor.deduplicate_chunks(chunks)

        # Apply max_chunks limit after deduplication, prioritizing by occurrence frequency
        if len(processed_chunks) > max_chunks:
            logger.info(f"Limiting from {len(processed_chunks)} to {max_chunks} chunks")
            # Prioritize chunks by occurrence frequency (how many times they appeared in search results)
            processed_chunks = self._prioritize_chunks_by_frequency(processed_chunks, page_frequencies, max_chunks)
        
        # Log page numbers of final chunks
        page_numbers = [chunk.page_number for chunk in processed_chunks]
        logger.info(f"Final chunk set: {len(processed_chunks)} chunks from pages: {sorted(set(page_numbers))}")
        
        return processed_chunks
    
    def _retrieve_with_search_queries(self,
                                    file_name: str,
                                    config: ExtractionConfiguration,
                                    max_chunks: int) -> tuple[List[ChunkData], dict]:
        """Retrieve chunks using search queries with frequency tracking."""
        # Ensure at least 10 chunks per query for comprehensive coverage
        min_chunks_per_query = 10
        max_chunks_per_query = max(min_chunks_per_query, max_chunks // len(config.search_queries))

        logger.info(f"Using {len(config.search_queries)} search queries with {max_chunks_per_query} chunks per query")

        chunks, page_frequencies = self.retriever.search_multiple_queries_with_frequencies(
            config.search_queries, file_name, max_chunks_per_query
        )

        return chunks, page_frequencies
    
    def _retrieve_all_pages(self, file_name: str, max_chunks: int) -> List[ChunkData]:
        """Retrieve all pages from the file."""
        logger.info(f"Retrieving all pages from {file_name} (limit: {max_chunks})")
        
        all_chunks = self.retriever.get_file_pages_ordered(file_name)
        chunks = all_chunks[:max_chunks]
        
        page_numbers = [chunk.page_number for chunk in chunks]
        logger.info(f"Retrieved {len(chunks)} chunks from pages: {sorted(set(page_numbers))}")

        return chunks

    def _prioritize_chunks_by_frequency(self, processed_chunks: List[ChunkData],
                                      page_frequencies: dict,
                                      max_chunks: int) -> List[ChunkData]:
        """
        Prioritize chunks by their occurrence frequency in search results.

        Args:
            processed_chunks: Deduplicated chunks to prioritize
            page_frequencies: Dictionary mapping page keys to their occurrence frequencies
            max_chunks: Maximum number of chunks to return

        Returns:
            Top chunks prioritized by occurrence frequency
        """
        logger.debug(f"Page frequencies from search results: {dict(sorted(page_frequencies.items(), key=lambda x: x[1], reverse=True)[:10])}")

        # Sort processed chunks by frequency (descending) and similarity score (descending)
        def chunk_priority(chunk):
            page_key = f"{chunk.file_name}_{chunk.page_number}"
            frequency = page_frequencies.get(page_key, 0)
            similarity = chunk.similarity_score or 0.0
            return (frequency, similarity, -chunk.page_number)  # Negative page for tie-breaking

        prioritized_chunks = sorted(processed_chunks, key=chunk_priority, reverse=True)

        # Log the prioritization for debugging
        top_chunks = prioritized_chunks[:max_chunks]
        for i, chunk in enumerate(top_chunks[:10]):  # Log top 10
            page_key = f"{chunk.file_name}_{chunk.page_number}"
            freq = page_frequencies.get(page_key, 0)
            logger.info(f"Priority {i+1}: Page {chunk.page_number} (frequency: {freq}, similarity: {chunk.similarity_score:.3f})")

        return top_chunks
