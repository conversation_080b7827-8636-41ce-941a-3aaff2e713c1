#!/usr/bin/env python3
"""
Gradio UI application for marine equipment data extraction.

This module provides a modern Gradio interface with configuration management,
improved user experience, and comprehensive extraction capabilities.
"""

import gradio as gr
import pandas as pd
import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime

from ..config.settings import settings
from ..config.config_manager import config_manager, ConfigManager
from ..core.base_classes import ExtractionType
from ..retrieval.retrieval_service import RetrievalService
from ..extraction.extraction_service import ExtractionService, ExtractionMethod
from ..core.utils import setup_logging, save_to_csv, save_to_json

# Import PDF processing function
try:
    from ingest_pdf import process_uploaded_pdf
except ImportError:
    def process_uploaded_pdf(pdf_file_path: str, progress_callback=None):
        return {"success": False, "message": "PDF processing not available", "stats": {}}

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


class ExtractionApp:
    """Main application class for the Gradio interface."""
    
    def __init__(self):
        """Initialize the application."""
        self.retrieval_service = None
        self.extractor = None
        self.current_chunks = []
        self.current_results = []
        self.config_manager = ConfigManager()
        self.last_processed_file = None

        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize application components."""
        try:
            # Initialize retrieval service
            self.retrieval_service = RetrievalService()
            logger.info("Retrieval service initialized")

            # Test connection
            if not self.retrieval_service.retriever.health_check():
                logger.error("Supabase connection failed")

        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
    
    def get_available_files(self) -> List[str]:
        """Get available files from database."""
        try:
            if self.retrieval_service:
                return self.retrieval_service.retriever.get_available_files()
            return []
        except Exception as e:
            logger.error(f"Failed to get available files: {e}")
            return []
    
    def get_available_models(self) -> Dict[str, List[str]]:
        """Get available models by provider."""
        return settings.get_available_models()
    
    def get_extraction_types(self) -> List[str]:
        """Get available extraction types."""
        return [et.value for et in ExtractionType]

    def get_config_for_editing(self, extraction_type: str) -> Tuple[str, str, str]:
        """
        Get configuration components for editing.

        Args:
            extraction_type: Type of extraction

        Returns:
            Tuple of (schema_json, system_prompt, search_queries_text)
        """
        try:
            extraction_enum = ExtractionType(extraction_type)
            config = config_manager.get_extraction_config(extraction_enum)

            # Format schema as JSON
            schema_json = json.dumps(config.schema, indent=2)

            # Format search queries as text (one per line)
            search_queries_text = '\n'.join(config.search_queries)

            return schema_json, config.system_prompt, search_queries_text

        except Exception as e:
            logger.error(f"Failed to get config for editing: {e}")
            return "{}", "", ""

    def save_config_changes(self, extraction_type: str, schema_json: str,
                          system_prompt: str, search_queries_text: str) -> str:
        """
        Save configuration changes.

        Args:
            extraction_type: Type of extraction
            schema_json: Schema as JSON string
            system_prompt: System prompt text
            search_queries_text: Search queries (one per line)

        Returns:
            Status message
        """
        try:
            extraction_enum = ExtractionType(extraction_type)

            # Validate and parse schema
            try:
                schema = json.loads(schema_json)
            except json.JSONDecodeError as e:
                return f"❌ Invalid JSON schema: {e}"

            # Parse search queries
            search_queries = [line.strip() for line in search_queries_text.split('\n')
                            if line.strip()]

            # Save to files
            config_manager = ConfigManager()

            # Save schema
            schema_file = config_manager.schemas_dir / f"{extraction_type}.json"
            with open(schema_file, 'w', encoding='utf-8') as f:
                json.dump(schema, f, indent=2)

            # Save system prompt
            prompt_file = config_manager.prompts_dir / f"{extraction_type}.txt"
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(system_prompt)

            # Save search queries
            queries_file = config_manager.queries_dir / f"{extraction_type}.txt"
            with open(queries_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(search_queries))

            # Clear cache to force reload
            config_manager._config_cache.clear()

            return f"✅ Configuration saved successfully for {extraction_type}"

        except Exception as e:
            logger.error(f"Failed to save config changes: {e}")
            return f"❌ Failed to save configuration: {e}"
    
    def perform_extraction(self, file_name: str, extraction_type: str, model_name: str,
                         use_search_queries: bool, max_chunks: int,
                         extraction_method: str = "auto") -> Tuple[str, str, str]:
        """
        Perform data extraction.
        
        Returns:
            Tuple of (status_message, results_json, results_csv)
        """
        try:
            if not file_name:
                return "Please select a file", "", ""
            
            if not extraction_type:
                return "Please select an extraction type", "", ""
            
            if not model_name:
                return "Please select a model", "", ""
            
            # Initialize extraction service with selected model
            self.extraction_service = ExtractionService(model_name=model_name)
            
            # Get extraction configuration
            extraction_enum = ExtractionType(extraction_type)
            config = config_manager.get_extraction_config(extraction_enum)
            
            # Retrieve chunks using shared service
            self.current_chunks = self.retrieval_service.retrieve_chunks(
                file_name, config, max_chunks, use_search_queries
            )

            if not self.current_chunks:
                return f"No chunks found for file: {file_name}", "", ""

            # Log page numbers of processed chunks
            page_numbers = [chunk.page_number for chunk in self.current_chunks]
            status_msg = f"Processing {len(self.current_chunks)} chunks with {model_name} from pages: {sorted(set(page_numbers))}"
            logger.info(status_msg)
            
            # Determine extraction method
            if extraction_method == "auto":
                selected_method = self.extraction_service.get_recommended_method(self.current_chunks)
                logger.info(f"Auto-selected {selected_method.value} extraction method for UI")
            else:
                selected_method = ExtractionMethod(extraction_method)
                logger.info(f"Using specified {selected_method.value} extraction method for UI")

            # Perform extraction
            result = self.extraction_service.extract(
                self.current_chunks,
                config,
                method=selected_method
            )
            
            if not result.success:
                return f"Extraction failed: {result.error}", "", ""
            
            self.current_results = result.data or []
            
            # Generate outputs
            results_json = json.dumps(self.current_results, indent=2)
            
            # Create CSV
            if self.current_results:
                df = pd.DataFrame(self.current_results)
                results_csv = df.to_csv(index=False)
            else:
                results_csv = ""
            
            # Create status message
            token_info = ""
            if result.token_usage:
                token_info = f" (Tokens: {result.token_usage.total_tokens}, Cost: ${result.token_usage.estimated_cost_usd:.4f})"
            
            status_msg = f"✅ Extraction completed: {len(self.current_results)} items extracted{token_info}"
            
            return status_msg, results_json, results_csv
            
        except Exception as e:
            error_msg = f"Extraction error: {str(e)}"
            logger.error(error_msg)
            return error_msg, "", ""
    
    def save_results(self, file_format: str) -> str:
        """Save current results to file."""
        try:
            if not self.current_results:
                return "No results to save"
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if file_format == "JSON":
                filename = f"extraction_results_{timestamp}.json"
                filepath = settings.output_dir / filename
                save_to_json(self.current_results, filepath)
            
            elif file_format == "CSV":
                filename = f"extraction_results_{timestamp}.csv"
                filepath = settings.output_dir / filename
                save_to_csv(self.current_results, filepath)
            
            else:
                return "Invalid file format"
            
            return f"Results saved to: {filepath}"
            
        except Exception as e:
            error_msg = f"Save error: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def download_extraction_json(self) -> Optional[str]:
        """
        Generate JSON file for extraction results download.

        Returns:
            Path to the generated JSON file or None if no results available
        """
        if not self.current_results:
            return None

        try:
            # Create downloads directory if it doesn't exist
            downloads_dir = Path("downloads")
            downloads_dir.mkdir(exist_ok=True)

            # Generate JSON file with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_path = downloads_dir / f"extraction_results_{timestamp}.json"

            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_results, f, indent=2, ensure_ascii=False)

            return str(json_path)

        except Exception as e:
            logger.error(f"Error generating extraction JSON: {e}")
            return None

    def download_extraction_csv(self) -> Optional[str]:
        """
        Generate CSV file for extraction results download.

        Returns:
            Path to the generated CSV file or None if no results available
        """
        if not self.current_results:
            return None

        try:
            # Create downloads directory if it doesn't exist
            downloads_dir = Path("downloads")
            downloads_dir.mkdir(exist_ok=True)

            # Generate CSV file with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_path = downloads_dir / f"extraction_results_{timestamp}.csv"

            # Convert to DataFrame and save as CSV
            df = pd.DataFrame(self.current_results)
            df.to_csv(csv_path, index=False)

            return str(csv_path)

        except Exception as e:
            logger.error(f"Error generating extraction CSV: {e}")
            return None

    def get_chunk_preview(self) -> str:
        """Get preview of current chunks."""
        if not self.current_chunks:
            return "No chunks loaded"
        
        preview_parts = []
        for i, chunk in enumerate(self.current_chunks[:3]):  # Show first 3 chunks
            preview_parts.append(f"**Chunk {i+1} (Page {chunk.page_number})**")
            preview_parts.append(chunk.text_raw[:500] + "..." if len(chunk.text_raw) > 500 else chunk.text_raw)
            preview_parts.append("---")
        
        if len(self.current_chunks) > 3:
            preview_parts.append(f"... and {len(self.current_chunks) - 3} more chunks")
        
        return "\n\n".join(preview_parts)

    def process_pdf_upload(self, pdf_file):
        """
        Process uploaded PDF file.

        Args:
            pdf_file: Uploaded PDF file from Gradio

        Returns:
            Tuple of (status_message, progress_log, processing_stats, csv_btn_update, json_btn_update)
        """
        if pdf_file is None:
            return "❌ No file selected", "", {}, gr.update(visible=False), gr.update(visible=False)

        try:
            # Progress tracking
            progress_log = []

            def progress_callback(message: str):
                progress_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
                return "\n".join(progress_log)

            progress_callback("Starting PDF upload processing...")

            # Process the PDF
            result = process_uploaded_pdf(pdf_file.name, progress_callback)

            if result["success"]:
                status_msg = f"✅ Successfully processed {result['file_name']}"
                progress_callback(f"Processing completed successfully!")
                progress_callback(f"Pages processed: {result['stats'].get('total_pages', 0)}")
                progress_callback(f"Pages with images: {result['stats'].get('pages_with_images', 0)}")
                progress_callback(f"OCR API calls: {result['stats'].get('ocr_calls', 0)}")

                # Store the processed file name for downloads
                self.last_processed_file = Path(pdf_file.name).stem

                # Show download buttons
                return status_msg, "\n".join(progress_log), result["stats"], gr.update(visible=True), gr.update(visible=True)
            else:
                status_msg = f"❌ Failed to process PDF: {result['message']}"
                progress_callback(f"Processing failed: {result['message']}")
                return status_msg, "\n".join(progress_log), result["stats"], gr.update(visible=False), gr.update(visible=False)

        except Exception as e:
            error_msg = f"❌ Error processing PDF: {str(e)}"
            logger.error(f"PDF processing error: {e}")
            return error_msg, f"Error: {str(e)}", {}, gr.update(visible=False), gr.update(visible=False)

    def download_processed_csv(self) -> Optional[str]:
        """
        Generate CSV file for the last processed PDF data.

        Returns:
            Path to the generated CSV file or None if no data available
        """
        if not self.last_processed_file:
            return None

        try:
            # Query the database for the processed data
            from ..retrieval.supabase_retriever import SupabaseRetriever
            retriever = SupabaseRetriever()

            # Get all chunks for the processed file
            response = retriever.supabase.table("pdf_documents").select("*").eq("file_name", f"{self.last_processed_file}.pdf").execute()

            if not response.data:
                return None

            # Convert to DataFrame and save as CSV
            df = pd.DataFrame(response.data)

            # Create downloads directory if it doesn't exist
            downloads_dir = Path("downloads")
            downloads_dir.mkdir(exist_ok=True)

            # Generate CSV file
            csv_path = downloads_dir / f"{self.last_processed_file}_processed_data.csv"
            df.to_csv(csv_path, index=False)

            return str(csv_path)

        except Exception as e:
            logger.error(f"Error generating CSV: {e}")
            return None

    def download_processed_json(self) -> Optional[str]:
        """
        Generate JSON file for the last processed PDF data.

        Returns:
            Path to the generated JSON file or None if no data available
        """
        if not self.last_processed_file:
            return None

        try:
            # Query the database for the processed data
            from ..retrieval.supabase_retriever import SupabaseRetriever
            retriever = SupabaseRetriever()

            # Get all chunks for the processed file
            response = retriever.supabase.table("pdf_documents").select("*").eq("file_name", f"{self.last_processed_file}.pdf").execute()

            if not response.data:
                return None

            # Create downloads directory if it doesn't exist
            downloads_dir = Path("downloads")
            downloads_dir.mkdir(exist_ok=True)

            # Generate JSON file
            json_path = downloads_dir / f"{self.last_processed_file}_processed_data.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(response.data, f, indent=2, ensure_ascii=False)

            return str(json_path)

        except Exception as e:
            logger.error(f"Error generating JSON: {e}")
            return None

    def create_interface(self) -> gr.Blocks:
        """Create the Gradio interface."""
        with gr.Blocks(
            title=settings.ui.title,
            theme=gr.themes.Soft(),
            css="""
            .gradio-container {
                max-width: 1200px !important;
            }
            .status-box {
                padding: 10px;
                border-radius: 5px;
                margin: 10px 0;
            }
            """
        ) as interface:
            
            gr.Markdown(f"# {settings.ui.title}")
            gr.Markdown(settings.ui.description)

            with gr.Tabs():
                with gr.Tab("🚀 Extraction"):
                    with gr.Row():
                        with gr.Column(scale=1):
                            gr.Markdown("## Configuration")

                            # File selection
                            file_dropdown = gr.Dropdown(
                                choices=self.get_available_files(),
                                label="Select PDF File",
                                info="Choose a PDF file from the database"
                            )

                            refresh_files_btn = gr.Button("🔄 Refresh Files", size="sm")

                            # Extraction type
                            extraction_dropdown = gr.Dropdown(
                                choices=self.get_extraction_types(),
                                label="Extraction Type",
                                info="Choose the type of data to extract"
                            )

                            # Model selection
                            available_models = self.get_available_models()
                            model_choices = []
                            for provider, models in available_models.items():
                                model_choices.extend(models)

                            model_dropdown = gr.Dropdown(
                                choices=model_choices,
                                label="LLM Model",
                                value=settings.models.default_model if settings.models.default_model in model_choices else None,
                                info="Choose the language model for extraction"
                            )

                            # Options
                            with gr.Accordion("Advanced Options", open=False):
                                use_search_queries = gr.Checkbox(
                                    label="Use Search Queries",
                                    value=True,
                                    info="Use predefined search queries for better chunk retrieval"
                                )

                                max_chunks = gr.Slider(
                                    minimum=1,
                                    maximum=50,
                                    value=settings.extraction.max_chunks,
                                    step=1,
                                    label="Max Chunks",
                                    info="Maximum number of chunks to process"
                                )

                                extraction_method = gr.Dropdown(
                                    choices=["auto", "batch", "incremental"],
                                    value="auto",
                                    label="Extraction Method",
                                    info="auto: recommended method, batch: all chunks at once, incremental: one chunk at a time with memory"
                                )

                            # Action buttons
                            extract_btn = gr.Button("🚀 Extract Data", variant="primary", size="lg")

                            with gr.Row():
                                save_json_btn = gr.Button("💾 Save JSON", size="sm")
                                save_csv_btn = gr.Button("📊 Save CSV", size="sm")
                                download_json_btn = gr.Button("📥 Download JSON", size="sm")
                                download_csv_btn = gr.Button("📥 Download CSV", size="sm")

                        with gr.Column(scale=2):
                            gr.Markdown("## Results")

                            # Status display
                            status_display = gr.Textbox(
                                label="Status",
                                value="Ready to extract data",
                                interactive=False,
                                elem_classes=["status-box"]
                            )

                            # Results tabs
                            with gr.Tabs():
                                with gr.Tab("📋 Extracted Data"):
                                    results_json = gr.Code(
                                        label="JSON Results",
                                        lines=20
                                    )

                                with gr.Tab("📊 CSV Preview"):
                                    results_csv = gr.Code(
                                        label="CSV Results",
                                        lines=20
                                    )

                                with gr.Tab("📄 Chunk Preview"):
                                    chunk_preview = gr.Markdown(
                                        value="No chunks loaded",
                                        label="Retrieved Chunks Preview"
                                    )

                            # Hidden components for file downloads
                            extraction_json_download = gr.File(label="JSON Download", visible=False)
                            extraction_csv_download = gr.File(label="CSV Download", visible=False)

                with gr.Tab("⚙️ Configuration Editor"):
                    gr.Markdown("## Edit Extraction Configuration")
                    gr.Markdown("Modify schemas, system prompts, and search queries for different extraction types.")

                    with gr.Row():
                        config_extraction_type = gr.Dropdown(
                            choices=self.get_extraction_types(),
                            value=self.get_extraction_types()[0] if self.get_extraction_types() else None,
                            label="Select Extraction Type to Edit",
                            info="Choose which configuration to modify"
                        )
                        load_config_btn = gr.Button("📥 Load Configuration", size="sm")

                    with gr.Row():
                        with gr.Column():
                            schema_editor = gr.Code(
                                label="JSON Schema - Define the structure of extracted data",
                                language="json",
                                lines=15
                            )

                        with gr.Column():
                            prompt_editor = gr.Textbox(
                                label="System Prompt - Instructions for the AI model",
                                lines=15
                            )

                    queries_editor = gr.Textbox(
                        label="Search Queries - Search terms used to find relevant content in PDFs (one per line)",
                        lines=10
                    )

                    with gr.Row():
                        save_config_btn = gr.Button("💾 Save Configuration", variant="primary")
                        config_status = gr.Textbox(
                            label="Configuration Status",
                            value="Ready to edit configuration",
                            interactive=False
                        )

                with gr.Tab("📤 Upload PDF"):
                    gr.Markdown("## Upload and Process PDF Documents")
                    gr.Markdown("Upload PDF files to process them with Mistral OCR and store in the database for extraction.")

                    with gr.Row():
                        with gr.Column(scale=1):
                            pdf_upload = gr.File(
                                label="Select PDF File",
                                file_types=[".pdf"],
                                file_count="single"
                            )

                            upload_btn = gr.Button("🚀 Process PDF", variant="primary", size="lg")

                            gr.Markdown("### Processing Options")
                            gr.Markdown("- **Mistral OCR**: Advanced OCR with image extraction")
                            gr.Markdown("- **Text Cleaning**: Removes pipes and extra whitespace to reduce token count")
                            gr.Markdown("- **LangSmith Tracking**: Monitors token usage and costs (if configured)")

                        with gr.Column(scale=2):
                            gr.Markdown("## Processing Status")

                            upload_status = gr.Textbox(
                                label="Status",
                                value="Ready to process PDF",
                                interactive=False,
                                elem_classes=["status-box"]
                            )

                            upload_progress = gr.Textbox(
                                label="Progress",
                                value="",
                                interactive=False,
                                lines=10
                            )

                            processing_stats = gr.JSON(
                                label="Processing Statistics",
                                value={}
                            )

                            # Download options
                            with gr.Row():
                                pdf_download_csv_btn = gr.Button("📊 Download CSV", size="sm", visible=False)
                                pdf_download_json_btn = gr.Button("💾 Download JSON", size="sm", visible=False)

                            # Hidden components for file downloads
                            csv_download = gr.File(label="CSV Download", visible=False)
                            json_download = gr.File(label="JSON Download", visible=False)

            # Event handlers
            refresh_files_btn.click(
                fn=lambda: gr.Dropdown(choices=self.get_available_files()),
                outputs=file_dropdown
            )
            
            extract_btn.click(
                fn=self.perform_extraction,
                inputs=[file_dropdown, extraction_dropdown, model_dropdown, use_search_queries, max_chunks, extraction_method],
                outputs=[status_display, results_json, results_csv]
            ).then(
                fn=self.get_chunk_preview,
                outputs=chunk_preview
            )
            
            save_json_btn.click(
                fn=lambda: self.save_results("JSON"),
                outputs=status_display
            )
            
            save_csv_btn.click(
                fn=lambda: self.save_results("CSV"),
                outputs=status_display
            )

            # Download event handlers for extraction results
            download_json_btn.click(
                fn=self.download_extraction_json,
                outputs=extraction_json_download
            )

            download_csv_btn.click(
                fn=self.download_extraction_csv,
                outputs=extraction_csv_download
            )

            # PDF upload event handler
            upload_btn.click(
                fn=self.process_pdf_upload,
                inputs=pdf_upload,
                outputs=[upload_status, upload_progress, processing_stats, pdf_download_csv_btn, pdf_download_json_btn]
            )

            # PDF download event handlers
            pdf_download_csv_btn.click(
                fn=self.download_processed_csv,
                outputs=csv_download
            )

            pdf_download_json_btn.click(
                fn=self.download_processed_json,
                outputs=json_download
            )

            # Configuration editor event handlers
            # Auto-load configuration when extraction type changes
            config_extraction_type.change(
                fn=self.get_config_for_editing,
                inputs=config_extraction_type,
                outputs=[schema_editor, prompt_editor, queries_editor]
            )

            # Manual load button for refresh
            load_config_btn.click(
                fn=self.get_config_for_editing,
                inputs=config_extraction_type,
                outputs=[schema_editor, prompt_editor, queries_editor]
            )

            save_config_btn.click(
                fn=self.save_config_changes,
                inputs=[config_extraction_type, schema_editor, prompt_editor, queries_editor],
                outputs=config_status
            )

        return interface


def create_app() -> gr.Blocks:
    """Create and return the Gradio application."""
    app = ExtractionApp()
    return app.create_interface()


def launch_app(share: bool = False, debug: bool = False, auth: tuple = None):
    """Launch the Gradio application."""
    try:
        # Validate configuration
        issues = settings.validate_configuration()
        if issues:
            logger.error("Configuration issues found:")
            for issue in issues:
                logger.error(f"  - {issue}")
            raise RuntimeError("Configuration validation failed")

        # Create and launch app
        app = create_app()

        # Set up authentication if provided
        launch_kwargs = {
            "share": share or settings.ui.share,
            "debug": debug or settings.ui.debug,
            "server_name": "0.0.0.0",
            "server_port": 7860
        }

        if auth:
            launch_kwargs["auth"] = auth
            logger.info("Authentication enabled for Gradio app")

        app.launch(**launch_kwargs)

    except Exception as e:
        logger.error(f"Failed to launch application: {e}")
        raise


if __name__ == "__main__":
    launch_app()
