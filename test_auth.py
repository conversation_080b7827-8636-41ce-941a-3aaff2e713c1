#!/usr/bin/env python3
"""
Test script for authentication functionality.

This script tests the authentication features added to the Gradio app.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_auth_parsing():
    """Test authentication parsing functionality."""
    print("🧪 Testing authentication parsing...")
    
    # Test command line auth parsing
    test_auth_string = "testuser:testpass123"
    try:
        username, password = test_auth_string.split(':', 1)
        auth_tuple = (username, password)
        print(f"✅ Auth string parsing: {auth_tuple}")
        assert username == "testuser"
        assert password == "testpass123"
    except Exception as e:
        print(f"❌ Auth string parsing failed: {e}")
        return False
    
    # Test invalid auth string
    try:
        invalid_auth = "invalidformat"
        username, password = invalid_auth.split(':', 1)
        print("❌ Should have failed for invalid format")
        return False
    except ValueError:
        print("✅ Correctly rejected invalid auth format")
    
    return True


def test_auth_file():
    """Test authentication file functionality."""
    print("🧪 Testing authentication file...")
    
    # Create a temporary auth file
    auth_file = Path("test_auth.txt")
    try:
        with open(auth_file, 'w') as f:
            f.write("fileuser:filepass456\n")
        
        # Test reading auth file
        with open(auth_file, 'r') as f:
            line = f.readline().strip()
            if ':' in line:
                username, password = line.split(':', 1)
                auth_tuple = (username, password)
                print(f"✅ Auth file parsing: {auth_tuple}")
                assert username == "fileuser"
                assert password == "filepass456"
            else:
                print("❌ Auth file format invalid")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Auth file test failed: {e}")
        return False
    finally:
        # Clean up
        if auth_file.exists():
            auth_file.unlink()


def test_env_auth():
    """Test environment variable authentication."""
    print("🧪 Testing environment authentication...")
    
    # Set test environment variables
    os.environ["GRADIO_USERNAME"] = "envuser"
    os.environ["GRADIO_PASSWORD"] = "envpass789"
    
    try:
        # Import the function from app.py
        from app import get_auth_from_env
        
        auth_tuple = get_auth_from_env()
        print(f"✅ Environment auth: {auth_tuple}")
        assert auth_tuple == ("envuser", "envpass789")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment auth test failed: {e}")
        return False
    finally:
        # Clean up environment variables
        os.environ.pop("GRADIO_USERNAME", None)
        os.environ.pop("GRADIO_PASSWORD", None)


def test_default_auth():
    """Test default authentication fallback."""
    print("🧪 Testing default authentication fallback...")
    
    try:
        # Import the function from app.py
        from app import get_auth_from_env
        
        # Ensure no auth env vars are set
        os.environ.pop("GRADIO_USERNAME", None)
        os.environ.pop("GRADIO_PASSWORD", None)
        
        auth_tuple = get_auth_from_env()
        print(f"✅ Default auth: {auth_tuple}")
        assert auth_tuple[0] == "admin"  # default username
        assert auth_tuple[1] == "marine2024"  # default password
        
        return True
        
    except Exception as e:
        print(f"❌ Default auth test failed: {e}")
        return False


def main():
    """Run all authentication tests."""
    print("🚀 Starting authentication tests...\n")
    
    tests = [
        test_auth_parsing,
        test_auth_file,
        test_env_auth,
        test_default_auth
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed\n")
            else:
                print("❌ Test failed\n")
        except Exception as e:
            print(f"❌ Test error: {e}\n")
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All authentication tests passed!")
        return True
    else:
        print("⚠️  Some authentication tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
