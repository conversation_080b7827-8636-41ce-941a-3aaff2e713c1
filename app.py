#!/usr/bin/env python3
"""
Hugging Face Spaces entry point for Marine Equipment Data Extraction System.

This file serves as the main entry point for deployment on Hugging Face Spaces.
It sets up authentication and launches the Gradio application.
"""

import os
import sys
import logging
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.ui.app import launch_app
from src.core.utils import setup_logging

# Setup logging
setup_logging(log_level="INFO")
logger = logging.getLogger(__name__)


def get_auth_from_env():
    """
    Get authentication credentials from environment variables.
    
    Returns:
        tuple: (username, password) if credentials are found, None otherwise
    """
    username = os.getenv("GRADIO_USERNAME")
    password = os.getenv("GRADIO_PASSWORD")
    
    if username and password:
        logger.info("Authentication credentials found in environment")
        return (username, password)
    
    # Fallback to default credentials for demo purposes
    # In production, always use environment variables
    default_username = os.getenv("DEFAULT_USERNAME", "admin")
    default_password = os.getenv("DEFAULT_PASSWORD", "marine2024")
    
    logger.warning("Using default authentication credentials. Set GRADIO_USERNAME and GRADIO_PASSWORD for production.")
    return (default_username, default_password)


def main():
    """Main entry point for Hugging Face Spaces."""
    try:
        logger.info("🚀 Starting Marine Equipment Data Extraction System on Hugging Face Spaces")

        # Get authentication credentials
        auth = get_auth_from_env()

        # Check if running on HF Spaces
        is_hf_spaces = os.getenv("SPACE_ID") is not None

        # Launch the application
        launch_app(
            share=is_hf_spaces,  # Enable sharing on HF Spaces
            debug=False,  # Disable debug mode in production
            auth=auth
        )

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise


if __name__ == "__main__":
    main()
