#!/usr/bin/env python3
"""
Tests for retrieval functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import List

from src.retrieval.supabase_retriever import SupabaseRetriever
from src.retrieval.chunk_processor import ChunkProcessor
from src.core.base_classes import ChunkData, RetrievalError


class TestSupabaseRetriever:
    """Test Supabase retriever functionality."""
    
    @pytest.fixture
    def mock_supabase_client(self):
        """Create mock Supabase client."""
        mock_client = Mock()
        mock_table = Mock()
        mock_client.table.return_value = mock_table
        return mock_client, mock_table
    
    @pytest.fixture
    def sample_chunks(self):
        """Create sample chunk data."""
        return [
            ChunkData(
                text="Sample text 1",
                text_raw="Sample text 1 raw",
                page_number=1,
                file_name="test.pdf",
                metadata={"source": "test"}
            ),
            ChunkData(
                text="Sample text 2",
                text_raw="Sample text 2 raw",
                page_number=2,
                file_name="test.pdf",
                metadata={"source": "test"}
            )
        ]
    
    @patch('src.retrieval.supabase_retriever.create_client')
    def test_retriever_initialization(self, mock_create_client):
        """Test retriever initialization."""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        retriever = SupabaseRetriever(
            supabase_url="test_url",
            supabase_key="test_key"
        )
        
        assert retriever.supabase_url == "test_url"
        assert retriever.supabase_key == "test_key"
        mock_create_client.assert_called_once_with("test_url", "test_key")
    
    def test_retriever_initialization_missing_credentials(self):
        """Test retriever initialization with missing credentials."""
        with pytest.raises(RetrievalError):
            SupabaseRetriever(supabase_url="", supabase_key="")
    
    @patch('src.retrieval.supabase_retriever.create_client')
    def test_get_available_files(self, mock_create_client):
        """Test getting available files."""
        mock_client = Mock()
        mock_table = Mock()
        mock_client.table.return_value = mock_table
        mock_create_client.return_value = mock_client
        
        # Mock response
        mock_response = Mock()
        mock_response.data = [
            {"file_name": "file1.pdf"},
            {"file_name": "file2.pdf"},
            {"file_name": "file1.pdf"}  # Duplicate
        ]
        mock_table.select.return_value.execute.return_value = mock_response
        
        retriever = SupabaseRetriever("test_url", "test_key")
        files = retriever.get_available_files()
        
        assert isinstance(files, list)
        assert len(files) == 2  # Duplicates removed
        assert "file1.pdf" in files
        assert "file2.pdf" in files
    
    @patch('src.retrieval.supabase_retriever.create_client')
    @patch('src.retrieval.supabase_retriever.OpenAI')
    def test_semantic_search(self, mock_openai, mock_create_client):
        """Test semantic search functionality."""
        # Setup mocks
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        mock_openai_client = Mock()
        mock_openai.return_value = mock_openai_client
        
        # Mock embedding response
        mock_embedding_response = Mock()
        mock_embedding_response.data = [Mock(embedding=[0.1, 0.2, 0.3])]
        mock_openai_client.embeddings.create.return_value = mock_embedding_response
        
        # Mock search response
        mock_search_response = Mock()
        mock_search_response.data = [
            {
                "text": "Sample text",
                "text_raw": "Sample text raw",
                "page_number": 1,
                "file_name": "test.pdf",
                "metadata": {},
                "similarity": 0.8
            }
        ]
        mock_client.rpc.return_value.execute.return_value = mock_search_response
        
        retriever = SupabaseRetriever("test_url", "test_key", "openai_key")
        chunks = retriever.semantic_search("test query", "test.pdf", 5)
        
        assert isinstance(chunks, list)
        assert len(chunks) == 1
        assert isinstance(chunks[0], ChunkData)
        assert chunks[0].similarity_score == 0.8
    
    @patch('src.retrieval.supabase_retriever.create_client')
    def test_get_file_pages_ordered(self, mock_create_client):
        """Test getting file pages in order."""
        mock_client = Mock()
        mock_table = Mock()
        mock_client.table.return_value = mock_table
        mock_create_client.return_value = mock_client
        
        # Mock response
        mock_response = Mock()
        mock_response.data = [
            {
                "text": "Page 1 text",
                "text_raw": "Page 1 text raw",
                "page_number": 1,
                "file_name": "test.pdf",
                "metadata": {}
            },
            {
                "text": "Page 2 text",
                "text_raw": "Page 2 text raw",
                "page_number": 2,
                "file_name": "test.pdf",
                "metadata": {}
            }
        ]
        
        mock_table.select.return_value.eq.return_value.order.return_value.execute.return_value = mock_response
        
        retriever = SupabaseRetriever("test_url", "test_key")
        chunks = retriever.get_file_pages_ordered("test.pdf")
        
        assert isinstance(chunks, list)
        assert len(chunks) == 2
        assert chunks[0].page_number == 1
        assert chunks[1].page_number == 2
    
    @patch('src.retrieval.supabase_retriever.create_client')
    def test_search_multiple_queries(self, mock_create_client):
        """Test searching with multiple queries."""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        retriever = SupabaseRetriever("test_url", "test_key")
        
        # Mock semantic_search method
        def mock_semantic_search(query, file_name, max_chunks):
            if query == "query1":
                return [ChunkData(text=f"Result for {query}", text_raw=f"Result for {query}", 
                                page_number=1, file_name=file_name)]
            elif query == "query2":
                return [ChunkData(text=f"Result for {query}", text_raw=f"Result for {query}", 
                                page_number=2, file_name=file_name)]
            return []
        
        with patch.object(retriever, 'semantic_search', side_effect=mock_semantic_search):
            chunks = retriever.search_multiple_queries(["query1", "query2"], "test.pdf", 5)
            
            assert isinstance(chunks, list)
            assert len(chunks) == 2
            assert chunks[0].page_number == 1
            assert chunks[1].page_number == 2
    
    @patch('src.retrieval.supabase_retriever.create_client')
    def test_health_check(self, mock_create_client):
        """Test health check functionality."""
        mock_client = Mock()
        mock_table = Mock()
        mock_client.table.return_value = mock_table
        mock_create_client.return_value = mock_client
        
        # Mock successful response
        mock_response = Mock()
        mock_response.data = [{"file_name": "test.pdf"}]
        mock_table.select.return_value.limit.return_value.execute.return_value = mock_response
        
        retriever = SupabaseRetriever("test_url", "test_key")
        health = retriever.health_check()
        
        assert health is True
    
    @patch('src.retrieval.supabase_retriever.create_client')
    def test_health_check_failure(self, mock_create_client):
        """Test health check failure."""
        mock_client = Mock()
        mock_table = Mock()
        mock_client.table.return_value = mock_table
        mock_create_client.return_value = mock_client
        
        # Mock exception
        mock_table.select.return_value.limit.return_value.execute.side_effect = Exception("Connection failed")
        
        retriever = SupabaseRetriever("test_url", "test_key")
        health = retriever.health_check()
        
        assert health is False


class TestChunkProcessor:
    """Test chunk processor functionality."""
    
    @pytest.fixture
    def sample_chunks(self):
        """Create sample chunks for testing."""
        return [
            ChunkData(
                text="This is a test chunk with some content",
                text_raw="This is a test chunk with some content",
                page_number=1,
                file_name="test1.pdf",
                metadata={},
                similarity_score=0.9
            ),
            ChunkData(
                text="Another test chunk with different content",
                text_raw="Another test chunk with different content",
                page_number=2,
                file_name="test1.pdf",
                metadata={},
                similarity_score=0.8
            ),
            ChunkData(
                text="This is a test chunk with some content",  # Duplicate
                text_raw="This is a test chunk with some content",
                page_number=1,
                file_name="test2.pdf",
                metadata={},
                similarity_score=0.7
            )
        ]
    
    def test_processor_initialization(self):
        """Test processor initialization."""
        processor = ChunkProcessor()
        
        assert processor.similarity_threshold == 0.8
        assert processor.min_chunk_length == 50
        assert processor.max_chunk_length == 10000
    
    def test_filter_chunks_by_file_names(self, sample_chunks):
        """Test filtering chunks by file names."""
        processor = ChunkProcessor()
        
        filtered = processor.filter_chunks(
            sample_chunks,
            {"file_names": ["test1.pdf"]}
        )
        
        assert len(filtered) == 2
        assert all(chunk.file_name == "test1.pdf" for chunk in filtered)
    
    def test_filter_chunks_by_page_range(self, sample_chunks):
        """Test filtering chunks by page range."""
        processor = ChunkProcessor()
        
        filtered = processor.filter_chunks(
            sample_chunks,
            {"page_range": (1, 1)}
        )
        
        assert len(filtered) == 2  # Two chunks on page 1
        assert all(chunk.page_number == 1 for chunk in filtered)
    
    def test_filter_chunks_by_similarity(self, sample_chunks):
        """Test filtering chunks by similarity score."""
        processor = ChunkProcessor()
        
        filtered = processor.filter_chunks(
            sample_chunks,
            {"min_similarity": 0.85}
        )
        
        assert len(filtered) == 1
        assert filtered[0].similarity_score >= 0.85
    
    def test_filter_chunks_by_keywords(self, sample_chunks):
        """Test filtering chunks by keywords."""
        processor = ChunkProcessor()
        
        filtered = processor.filter_chunks(
            sample_chunks,
            {"keywords": ["different"]}
        )
        
        assert len(filtered) == 1
        assert "different" in filtered[0].text_raw.lower()
    
    def test_deduplicate_chunks(self, sample_chunks):
        """Test chunk deduplication."""
        processor = ChunkProcessor()
        
        deduplicated = processor.deduplicate_chunks(sample_chunks)
        
        # Should remove exact duplicates
        assert len(deduplicated) < len(sample_chunks)
        
        # Check that duplicates are actually removed
        texts = [chunk.text_raw for chunk in deduplicated]
        assert len(texts) == len(set(texts))  # All texts should be unique
    
    def test_sort_chunks(self, sample_chunks):
        """Test chunk sorting."""
        processor = ChunkProcessor()
        
        # Sort by page number
        sorted_chunks = processor.sort_chunks(sample_chunks, "page_number")
        
        assert len(sorted_chunks) == len(sample_chunks)
        # Should be sorted by file name first, then page number
        assert sorted_chunks[0].file_name <= sorted_chunks[-1].file_name
    
    def test_sort_chunks_by_similarity(self, sample_chunks):
        """Test sorting chunks by similarity score."""
        processor = ChunkProcessor()
        
        sorted_chunks = processor.sort_chunks(sample_chunks, "similarity_score", reverse=True)
        
        assert len(sorted_chunks) == len(sample_chunks)
        # Should be sorted by similarity score in descending order
        scores = [chunk.similarity_score or 0.0 for chunk in sorted_chunks]
        assert scores == sorted(scores, reverse=True)
    
    def test_aggregate_chunks(self, sample_chunks):
        """Test chunk aggregation."""
        processor = ChunkProcessor()
        
        # Split chunks into multiple lists
        chunk_lists = [
            sample_chunks[:2],
            sample_chunks[2:]
        ]
        
        aggregated = processor.aggregate_chunks(chunk_lists)
        
        assert isinstance(aggregated, list)
        # Should be deduplicated
        assert len(aggregated) <= len(sample_chunks)
    
    def test_get_chunk_statistics(self, sample_chunks):
        """Test getting chunk statistics."""
        processor = ChunkProcessor()
        
        stats = processor.get_chunk_statistics(sample_chunks)
        
        assert isinstance(stats, dict)
        assert stats["total_chunks"] == len(sample_chunks)
        assert stats["unique_files"] == 2
        assert "file_names" in stats
        assert "page_range" in stats
        assert "avg_text_length" in stats
    
    def test_calculate_text_similarity(self):
        """Test text similarity calculation."""
        processor = ChunkProcessor()
        
        text1 = "This is a test document with some content"
        text2 = "This is a test document with different content"
        text3 = "Completely different text here"
        
        # Similar texts should have high similarity
        similarity1 = processor._calculate_text_similarity(text1, text2)
        assert 0.5 < similarity1 < 1.0
        
        # Different texts should have low similarity
        similarity2 = processor._calculate_text_similarity(text1, text3)
        assert similarity2 < 0.5
        
        # Identical texts should have similarity of 1.0
        similarity3 = processor._calculate_text_similarity(text1, text1)
        assert similarity3 == 1.0


if __name__ == "__main__":
    pytest.main([__file__])
