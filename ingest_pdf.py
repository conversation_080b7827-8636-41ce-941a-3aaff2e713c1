#!/usr/bin/env python3
"""
PDF Ingestion Script using Mistral OCR

This script:
1. Sends PDF to Mistral OCR
2. Replaces image references in markdown with base64 image data (correct MIME)
3. Generates embeddings (text-only)
4. Stores into Supabase with pgvector
"""

import argparse
import os
import base64
import re
from pathlib import Path
from typing import Dict, List, Optional, Any

from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client, Client
from mistralai import Mistral

# Import LangSmith for tracking (if available)
try:
    from src.core.langsmith_integration import langsmith_tracker, log_llm_call
    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False
    def log_llm_call(operation: str, model_name: str):
        def decorator(func):
            return func
        return decorator

# Load env vars
load_dotenv()
os.makedirs("cleaned_pages", exist_ok=True)

# Detect MIME type from base64 image string
def detect_mime_type(b64_string: str) -> str:
        return "image/png"

# Replace image refs like ![img-1](img-1) with data URIs
def replace_images_in_markdown(markdown_str: str, images_dict: dict) -> str:
    """
    Replace image placeholders in markdown with base64-encoded images.

    Args:
        markdown_str: Markdown text containing image placeholders
        images_dict: Dictionary mapping image IDs to base64 strings

    Returns:
        Markdown text with images replaced by base64 data
    """
    for img_name, base64_str in images_dict.items():
        markdown_str = markdown_str.replace(
            f"![{img_name}]({img_name})", f"![{img_name}]({base64_str})"
        )
    return markdown_str


class PDFProcessor:
    def __init__(self):
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.mistral_client = Mistral(api_key=os.getenv("MISTRAL_API_KEY"))
        self.supabase: Client = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_KEY")
        )
        self.embedding_model = os.getenv("EMBEDDING_MODEL")
        self.mistral_ocr_model = os.getenv("MISTRAL_OCR_MODEL")

        self.stats = {
            "total_pages": 0,
            "pages_with_images": 0,
            "ocr_calls": 0
        }

    def setup_database(self):
        print("✅ Assuming Supabase table 'pdf_documents' exists")

    def pdf_to_base64(self, pdf_path: str) -> Optional[str]:
        try:
            with open(pdf_path, "rb") as f:
                return base64.b64encode(f.read()).decode("utf-8")
        except Exception as e:
            print(f"❌ Error reading PDF: {e}")
            return None

    def process_pdf_with_mistral_ocr(self, pdf_path: str) -> Optional[Dict]:
        self.stats["ocr_calls"] += 1
        pdf_base64 = self.pdf_to_base64(pdf_path)
        if not pdf_base64:
            return None

        print("🚀 Sending PDF to Mistral OCR API...")
        try:
            response = self.mistral_client.ocr.process(
                model=self.mistral_ocr_model,
                document={
                    "type": "document_url",
                    "document_url": f"data:application/pdf;base64,{pdf_base64}"
                },
                include_image_base64=True
            )
            print("✅ OCR complete")
            return {"pages": response.pages}
        except Exception as e:
            print(f"❌ OCR error: {e}")
            return None

    @log_llm_call("embedding", "text-embedding-3-small")
    def get_embedding(self, text: str) -> List[float]:
        # Remove pipes and other characters that might increase token count
        cleaned_text = self._clean_text_for_embedding(text)

        # Log token count estimation before embedding
        estimated_tokens = len(cleaned_text.split()) * 1.3  # Rough estimate
        print(f"📊 Estimated tokens for embedding: {estimated_tokens:.0f}")

        try:
            response = self.openai_client.embeddings.create(
                input=cleaned_text,
                model=self.embedding_model
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"⚠️  Embedding error: {e}")
            return []

    def _clean_text_for_embedding(self, text: str) -> str:
        """Clean text by removing pipes and other characters that increase token count."""
        # Remove table pipes and extra whitespace
        cleaned = re.sub(r'\|+', ' ', text)  # Replace multiple pipes with space
        cleaned = re.sub(r'\s+', ' ', cleaned)  # Replace multiple spaces with single space
        cleaned = re.sub(r'^\s+|\s+$', '', cleaned, flags=re.MULTILINE)  # Strip line whitespace
        cleaned = cleaned.strip()

        print(f"📊 Text length before cleaning: {len(text)} chars")
        print(f"📊 Text length after cleaning: {len(cleaned)} chars")

        return cleaned

    def ingest_document(self, file_path: str):
        file_name = Path(file_path).name
        base_name = Path(file_path).stem
        print(f"📄 Ingesting {file_name}")

        ocr_result = self.process_pdf_with_mistral_ocr(file_path)
        if not ocr_result or "pages" not in ocr_result:
            print("❌ No OCR result")
            return

        pages = ocr_result["pages"]
        self.stats["total_pages"] = len(pages)

        for i, page in enumerate(pages):
            page_num = i + 1
            markdown = page.markdown
            images = page.images or []

            print(f"📑 Page {page_num}/{self.stats['total_pages']}")
            print(f"📊 Original markdown length: {len(markdown)} characters")

            if images:
                self.stats["pages_with_images"] += 1
                print(f"🖼️  Found {len(images)} images on page {page_num}")

            # Use original markdown directly for embedding (no image injection needed)
            text_for_embedding = markdown
            print(f"📊 Original text length: {len(text_for_embedding)} characters")

            # Check if text is too long for embedding (rough estimate: 1 token ≈ 2 characters for safety)
            max_chars = 7000 * 2  # Conservative estimate for 7000 tokens (leaving buffer)
            if len(text_for_embedding) > max_chars:
                print(f"⚠️  Text too long for embedding ({len(text_for_embedding)} chars), truncating to {max_chars} chars")
                text_for_embedding = text_for_embedding[:max_chars]

            # Create version with base64 images for UI display
            image_map = {img.id: img.image_base64 for img in images}
            markdown_with_images = replace_images_in_markdown(markdown, image_map)
            print(f"📊 Markdown with images length: {len(markdown_with_images)} characters")

            # Save cleaned content (with images for UI)
            cleaned_path = f"cleaned_pages/{base_name}_page_{page_num}.md"
            with open(cleaned_path, "w", encoding="utf-8") as f:
                f.write(markdown_with_images)

            # Embedding
            embedding = self.get_embedding(text_for_embedding)

            metadata = {
                "file_name": file_name,
                "page_number": page_num,
                "source": "mistral_ocr"
            }

            # Store in Supabase
            try:
                result = self.supabase.table("pdf_documents").insert({
                    "file_name": file_name,
                    "page_number": page_num,
                    "text": markdown_with_images,   # UI-ready markdown
                    "text_raw": markdown,           # Original from Mistral
                    "metadata": metadata,
                    "embedding": embedding
                }).execute()

                if result.data:
                    print(f"✅ Stored page {page_num}")
                else:
                    print(f"❌ Supabase insert failed: {result.count}")
            except Exception as e:
                print(f"❌ Supabase error: {e}")

        print(f"\n📊 Ingestion Summary:")
        print(f"   Pages processed:    {self.stats['total_pages']}")
        print(f"   Pages w/ images:    {self.stats['pages_with_images']}")
        print(f"   OCR API calls:      {self.stats['ocr_calls']}")


def process_uploaded_pdf(pdf_file_path: str, progress_callback=None) -> Dict[str, Any]:
    """
    Process an uploaded PDF file for use in the UI.

    Args:
        pdf_file_path: Path to the uploaded PDF file
        progress_callback: Optional callback function for progress updates

    Returns:
        Dictionary with processing results and statistics
    """
    try:
        processor = PDFProcessor()
        processor.setup_database()

        if progress_callback:
            progress_callback("Starting PDF processing...")

        # Process the document
        processor.ingest_document(pdf_file_path)

        if progress_callback:
            progress_callback("PDF processing completed successfully!")

        return {
            "success": True,
            "message": "PDF processed successfully",
            "stats": processor.stats,
            "file_name": Path(pdf_file_path).name
        }

    except Exception as e:
        error_msg = f"Error processing PDF: {str(e)}"
        if progress_callback:
            progress_callback(error_msg)

        return {
            "success": False,
            "message": error_msg,
            "stats": {},
            "file_name": Path(pdf_file_path).name if pdf_file_path else "unknown"
        }


def main():
    parser = argparse.ArgumentParser(description="Ingest PDF with Mistral OCR + store in Supabase.")
    parser.add_argument("pdf_path", help="Path to PDF file.")
    args = parser.parse_args()

    processor = PDFProcessor()
    processor.setup_database()
    processor.ingest_document(args.pdf_path)


if __name__ == "__main__":
    main()
