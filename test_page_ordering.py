#!/usr/bin/env python3
"""
Test script to verify page ordering functionality in structured data extraction.

This script tests:
1. Page retrieval in ascending order
2. Semantic search with page order preservation
3. Data extraction maintaining document sequence
"""

import os
import sys
from dotenv import load_dotenv

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from structured_query import StructuredDataExtractor

load_dotenv()

def test_page_ordering():
    """Test that pages are retrieved in correct ascending order."""
    print("🧪 Testing Page Ordering System...")
    
    extractor = StructuredDataExtractor()
    
    # Get available files
    files = extractor.get_available_files()
    if not files:
        print("❌ No files found in database")
        return False
    
    print(f"📁 Testing with file: {files[0]}")
    
    # Test 1: Get pages in order
    print("\n1️⃣ Testing sequential page retrieval...")
    pages = extractor.get_file_pages_ordered(files[0])
    
    if not pages:
        print("❌ No pages retrieved")
        return False
    
    # Verify ascending order
    page_numbers = [p.get('page_number', 0) for p in pages]
    is_ascending = all(page_numbers[i] <= page_numbers[i+1] for i in range(len(page_numbers)-1))
    
    print(f"   📄 Retrieved {len(pages)} pages")
    print(f"   📊 Page numbers: {page_numbers[:10]}{'...' if len(page_numbers) > 10 else ''}")
    print(f"   ✅ Ascending order: {'Yes' if is_ascending else 'No'}")
    
    if not is_ascending:
        print("❌ Pages not in ascending order!")
        return False
    
    # Test 2: Semantic search with order preservation
    print("\n2️⃣ Testing semantic search with page order...")
    search_results = extractor.semantic_search("parts list", files[0], limit=5)
    
    if search_results:
        search_pages = [r.get('page_number', 0) for r in search_results]
        print(f"   🔍 Found {len(search_results)} relevant chunks")
        print(f"   📊 Page numbers: {search_pages}")
        
        # Check if results are sorted by page number
        is_search_ordered = all(search_pages[i] <= search_pages[i+1] for i in range(len(search_pages)-1))
        print(f"   ✅ Search results ordered: {'Yes' if is_search_ordered else 'No'}")
    else:
        print("   ⚠️  No search results found")
    
    # Test 3: Sample extraction with page order
    print("\n3️⃣ Testing extraction with page order preservation...")
    
    sample_schema = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "content_summary": {"type": "string"},
                "page_number": {"type": "number"},
                "has_tables": {"type": "boolean"}
            }
        }
    }
    
    # Use first 3 pages for quick test
    test_chunks = pages[:3]
    result = extractor.extract_structured_data(
        test_chunks, 
        sample_schema, 
        "Summarize the content of each page and indicate if it contains tables",
        preserve_page_order=True
    )
    
    if result.get('success'):
        extracted_data = result['data']
        if isinstance(extracted_data, list):
            extracted_pages = [item.get('page_number', 0) for item in extracted_data if isinstance(item, dict)]
            print(f"   📊 Extracted data for pages: {extracted_pages}")
            print(f"   ✅ Extraction successful with {len(extracted_data)} items")
        else:
            print(f"   ✅ Extraction successful (single object)")
    else:
        print(f"   ❌ Extraction failed: {result.get('error', 'Unknown error')}")
    
    print("\n🎉 Page ordering tests completed!")
    return True

def test_file_statistics():
    """Display statistics about available files."""
    print("\n📊 File Statistics...")
    
    extractor = StructuredDataExtractor()
    files = extractor.get_available_files()
    
    for file_name in files:
        pages = extractor.get_file_pages_ordered(file_name)
        if pages:
            page_count = len(pages)
            min_page = min(p.get('page_number', 0) for p in pages)
            max_page = max(p.get('page_number', 0) for p in pages)
            
            # Check for text content
            text_lengths = [len(p.get('text', '')) for p in pages]
            avg_text_length = sum(text_lengths) / len(text_lengths) if text_lengths else 0
            
            print(f"\n📄 {file_name}:")
            print(f"   Pages: {page_count} (range: {min_page}-{max_page})")
            print(f"   Avg text length: {avg_text_length:.0f} chars")
            print(f"   Sample page numbers: {[p.get('page_number') for p in pages[:5]]}")

if __name__ == "__main__":
    print("🚀 Starting Page Ordering Tests...")
    
    try:
        # Run tests
        success = test_page_ordering()
        test_file_statistics()
        
        if success:
            print("\n✅ All tests passed! Page ordering system is working correctly.")
        else:
            print("\n❌ Some tests failed. Check the output above.")
            
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc()
