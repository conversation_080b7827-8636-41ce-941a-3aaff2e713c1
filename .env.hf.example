# Hugging Face Spaces Environment Configuration
# Copy this file to .env and fill in your actual values
# For HF Spaces, set these as Secrets in your Space settings

# Authentication (Required for HF Spaces)
GRADIO_USERNAME=admin
GRA<PERSON>O_PASSWORD=marine2024

# AI Model API Keys (At least one required)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here

# Database Configuration (Required)
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_service_key

# Optional: LangSmith Integration for monitoring
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key
LANGCHAIN_PROJECT=marine-equipment-extraction

# Optional: Default model configuration
DEFAULT_MODEL=gpt-4o-mini

# Optional: Debug settings (set to false for production)
DEBUG=false
LOG_LEVEL=INFO

# HF Spaces specific settings
HF_SPACES=true
GRADIO_SERVER_NAME=0.0.0.0
GRADIO_SERVER_PORT=7860
