{"type": "object", "properties": {"maintenance_jobs": {"type": "array", "items": {"type": "object", "properties": {"equipment_name": {"type": "string", "description": "Major system name (e.g., Air Compressor, Freshwater Generator)"}, "job_body": {"type": "string", "description": "Component or sub-system (e.g., Valves, Oil Filter, Cylinder, General Check)"}, "job_action": {"type": "string", "description": "Maintenance activity (e.g., Inspect, Replace, Clean, Check, Service)"}, "frequency": {"type": "string", "description": "Numeric value only (e.g., 1, 250, 1000, 2000, 8000, 9000)"}, "frequency_type": {"type": "string", "description": "One of: Hours, Days, Weeks, Months, Years", "enum": ["Hours", "Days", "Weeks", "Months", "Years"]}, "job_procedure": {"type": "string", "description": "Combined procedures in bullet list format using HTML <br> tags (e.g., '- Inspect piping for leaks.<br>- Check oil level.<br>- Observe cooling water and noise levels.')"}, "pdf_reference": {"type": "string", "description": "Section title or page number (e.g., 'Section 7.1, Table 6', 'Page 15', 'Maintenance Schedule Table')"}}, "required": ["equipment_name", "job_body", "job_action", "frequency", "frequency_type", "job_procedure", "pdf_reference"]}}}, "required": ["maintenance_jobs"]}