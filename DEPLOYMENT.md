# 🚀 Hugging Face Spaces Deployment Guide

This guide walks you through deploying the Marine Equipment Data Extraction System to Hugging Face Spaces.

## 📋 Prerequisites

1. **Hugging Face Account**: Create an account at [huggingface.co](https://huggingface.co)
2. **API Keys**: Obtain API keys for at least one AI provider:
   - OpenAI API key
   - Anthropic API key  
   - Mistral API key
3. **Supabase Database**: Set up a Supabase project with pgvector extension

## 🛠️ Step-by-Step Deployment

### 1. Create a New Space

1. Go to [Hugging Face Spaces](https://huggingface.co/spaces)
2. Click "Create new Space"
3. Fill in the details:
   - **Space name**: `marine-equipment-extraction` (or your preferred name)
   - **License**: MIT
   - **SDK**: Gradio
   - **Hardware**: CPU Basic (free tier) or upgrade as needed
   - **Visibility**: Public or Private (your choice)

### 2. Upload Files

Upload the following files to your Space:

**Required Files:**
- `app.py` - Main entry point for HF Spaces
- `requirements.txt` - Python dependencies
- `README_HF.md` - Rename this to `README.md` in your Space
- `src/` - Entire src directory with all Python modules
- `data/` - Configuration data (schemas, prompts, queries)

**Optional Files:**
- `sample/` - Sample PDF files for testing
- `.env.hf.example` - Environment variable template

### 3. Configure Environment Variables (Secrets)

In your Space settings, add the following secrets:

**Authentication (Required):**
```
GRADIO_USERNAME=your_username
GRADIO_PASSWORD=your_secure_password
```

**AI Provider API Keys (At least one required):**
```
OPENAI_API_KEY=sk-your-openai-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key
MISTRAL_API_KEY=your-mistral-key
```

**Database Configuration (Required):**
```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-service-key
```

**Optional Configuration:**
```
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your-langsmith-key
LANGCHAIN_PROJECT=marine-equipment-extraction
DEFAULT_MODEL=gpt-4o-mini
DEBUG=false
LOG_LEVEL=INFO
```

**Note:** You do NOT need to set `HF_SPACES`, `GRADIO_SERVER_NAME`, or `GRADIO_SERVER_PORT` as environment variables. Hugging Face Spaces automatically sets `SPACE_ID` which the app uses to detect HF Spaces deployment and configure itself accordingly.

### 4. Set Up Supabase Database

1. Create a new Supabase project
2. Enable the pgvector extension:
   ```sql
   CREATE EXTENSION IF NOT EXISTS vector;
   ```

3. Create the pdf_documents table:
   ```sql
   CREATE TABLE pdf_documents (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       file_name TEXT NOT NULL,
       page_number INTEGER NOT NULL,
       text_raw TEXT,
       text TEXT,
       metadata JSONB,
       embedding VECTOR(1536),
       created_at TIMESTAMP DEFAULT NOW()
   );
   ```

4. Create the similarity search function:
   ```sql
   CREATE OR REPLACE FUNCTION match_documents(
       query_embedding vector(1536),
       match_threshold float,
       match_count int,
       input_file_name text
   ) RETURNS TABLE (
       id uuid,
       file_name text,
       page_number int,
       text_raw text,
       text text,
       metadata jsonb,
       similarity float
   ) AS $$
   BEGIN
       RETURN QUERY
       SELECT
           pdf_documents.id,
           pdf_documents.file_name,
           pdf_documents.page_number,
           pdf_documents.text_raw,
           pdf_documents.text,
           pdf_documents.metadata,
           1 - (pdf_documents.embedding <=> query_embedding) AS similarity
       FROM pdf_documents
       WHERE pdf_documents.file_name = input_file_name
         AND 1 - (pdf_documents.embedding <=> query_embedding) > match_threshold
       ORDER BY pdf_documents.embedding <=> query_embedding
       LIMIT match_count;
   END;
   $$ LANGUAGE plpgsql;
   ```

### 5. Deploy and Test

1. Commit your files to the Space repository
2. Wait for the build to complete (check the logs)
3. Access your Space URL
4. Test authentication with your configured credentials
5. Upload a sample PDF and test extraction

## 🔧 Troubleshooting

### Common Issues

**Build Failures:**
- Check requirements.txt for version conflicts
- Ensure all files are properly uploaded
- Review build logs for specific errors

**Authentication Issues:**
- Verify GRADIO_USERNAME and GRADIO_PASSWORD are set correctly
- Check that secrets are properly configured in Space settings

**API Connection Errors:**
- Verify API keys are valid and have sufficient credits
- Check that at least one AI provider key is configured
- Ensure Supabase URL and key are correct

**Database Errors:**
- Verify pgvector extension is enabled
- Check that tables and functions are created correctly
- Ensure Supabase service key has proper permissions

### Performance Optimization

**For Better Performance:**
- Upgrade to GPU hardware for faster processing
- Use CPU Upgrade for more memory and processing power
- Consider persistent storage for caching

**Memory Management:**
- Monitor memory usage in Space logs
- Reduce max_chunks parameter if needed
- Use batch processing for large documents

## 📊 Monitoring

### Usage Tracking
- Monitor API usage through provider dashboards
- Track token consumption and costs
- Use LangSmith for detailed tracing (optional)

### Space Analytics
- Check Space analytics in HF dashboard
- Monitor user engagement and usage patterns
- Review build and runtime logs regularly

## 🔒 Security Best Practices

1. **Use Strong Authentication**: Set secure username/password combinations
2. **Protect API Keys**: Never commit API keys to the repository
3. **Regular Key Rotation**: Rotate API keys periodically
4. **Monitor Usage**: Keep track of API usage and costs
5. **Access Control**: Use private Spaces for sensitive data

## 📈 Scaling Considerations

**For High Usage:**
- Consider upgrading to persistent storage
- Use GPU hardware for faster processing
- Implement caching strategies
- Consider rate limiting for API calls

**For Enterprise Use:**
- Set up private Spaces
- Implement additional authentication layers
- Consider on-premises deployment
- Set up monitoring and alerting

---

**Need Help?** Check the [Hugging Face Spaces documentation](https://huggingface.co/docs/hub/spaces) or contact support.
