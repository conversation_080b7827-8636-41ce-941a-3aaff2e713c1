#!/usr/bin/env python3
"""
Test script for the updated structured data extraction system with:
1. Smart search query generation
2. LangChain structured data extraction
3. Predefined search queries from folders
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append('.')

from structured_query import load_available_extractions, generate_smart_search_query, StructuredDataExtractor
from openai import OpenAI

def test_search_query_generation():
    """Test the smart search query generation."""
    print("🧪 Testing Smart Search Query Generation")
    print("=" * 50)
    
    # Load extractions
    extractions = load_available_extractions()
    print(f"📁 Loaded {len(extractions)} extraction types: {list(extractions.keys())}")
    
    if 'spare_parts' in extractions:
        spare_parts_config = extractions['spare_parts']
        search_queries = spare_parts_config.get('search_queries', [])
        print(f"🔍 Found {len(search_queries)} predefined search queries")
        print(f"📝 Sample queries: {search_queries[:5]}")
        
        # Test smart query generation
        openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        smart_query = generate_smart_search_query('spare_parts', search_queries, openai_client)
        print(f"🧠 Generated smart query: '{smart_query}'")
    
    print()

def test_langchain_extraction():
    """Test LangChain-based extraction."""
    print("🧪 Testing LangChain Extraction")
    print("=" * 50)
    
    # Initialize extractor
    extractor = StructuredDataExtractor()
    
    # Get available files first
    files = extractor.get_available_files()
    print(f"📁 Available files: {files}")

    if not files:
        print("❌ No files available for testing")
        return

    # Get a small sample of data
    test_file = files[0]  # Use first available file
    chunks = extractor.get_file_pages_ordered(test_file)[:3]  # First 3 pages
    print(f"📄 Testing with {len(chunks)} pages")
    
    if chunks:
        # Test LangChain extraction
        result = extractor.extract_with_langchain(
            chunks=chunks,
            extraction_type="spare_parts",
            system_prompt="""Extract spare parts information from the provided content. 
            For each spare part found, extract: equipment_name, part_name, part_number, 
            drawing_number, position_number, quantity, units, materials, remarks, 
            spare_part_title, and pdf_reference (page number)."""
        )
        
        print(f"✅ Extraction result: {result.get('success', False)}")
        print(f"📊 Extracted {len(result.get('data', []))} items")
        print(f"🔧 Method: {result.get('extraction_method', 'unknown')}")
        
        # Show first item if available
        if result.get('data'):
            first_item = result['data'][0]
            print(f"📋 Sample item: {first_item}")
    
    print()

def test_complete_workflow():
    """Test the complete workflow."""
    print("🧪 Testing Complete Workflow")
    print("=" * 50)
    
    # Load extractions
    extractions = load_available_extractions()
    
    if 'spare_parts' in extractions:
        extraction_config = extractions['spare_parts']
        
        # Initialize extractor
        extractor = StructuredDataExtractor()
        
        # Generate smart search query
        search_queries = extraction_config.get('search_queries', [])
        smart_query = generate_smart_search_query('spare_parts', search_queries, extractor.openai_client)
        print(f"🧠 Smart query: '{smart_query}'")
        
        # Get available files
        files = extractor.get_available_files()
        if not files:
            print("❌ No files available")
            return

        test_file = files[0]
        print(f"📁 Testing with file: {test_file}")

        # Perform semantic search
        chunks = extractor.semantic_search(smart_query, test_file, 5)
        print(f"🔍 Found {len(chunks)} relevant chunks")
        
        if chunks:
            # Extract using LangChain
            result = extractor.extract_with_langchain(
                chunks=chunks,
                extraction_type="spare_parts",
                system_prompt=extraction_config['system_prompt']
            )
            
            print(f"✅ Final result: {result.get('success', False)}")
            print(f"📊 Total extracted: {len(result.get('data', []))} items")
            
            # Show summary
            if result.get('data'):
                data = result['data']
                print(f"📈 Summary:")
                print(f"   - Total parts: {len(data)}")
                print(f"   - Pages covered: {len(set(item.get('pdf_reference', 0) for item in data))}")
                print(f"   - Sample part: {data[0].get('part_name', 'N/A')}")

if __name__ == "__main__":
    print("🚀 Testing Updated Structured Data Extraction System")
    print("=" * 60)
    
    try:
        test_search_query_generation()
        test_langchain_extraction()
        test_complete_workflow()
        
        print("✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
